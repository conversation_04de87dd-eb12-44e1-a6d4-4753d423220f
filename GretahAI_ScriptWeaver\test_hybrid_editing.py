"""
Test Hybrid AI-Assisted Test Case Editing System

This test verifies that the hybrid editing system works correctly,
including step templates, merging, validation, and state management.
"""

import pytest
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.step_templates import STEP_TEMPLATES, get_templates_by_category, create_custom_step_template
from core.step_merger import Step<PERSON>er<PERSON>, merge_ai_and_manual_steps
from helpers.step_validation import validate_combined_steps, get_validation_summary
from state_manager import StateManager


class TestStepTemplates:
    """Test step template functionality."""

    def test_template_creation(self):
        """Test that templates can be created correctly."""
        # Test navigation template
        nav_template = STEP_TEMPLATES["nav_to_url"]
        step = nav_template.create_step("1", {"locator": "https://test.com"})

        assert step["step_no"] == "1"
        assert step["action"] == "navigate"
        assert step["locator"] == "https://test.com"
        assert step["_is_manual"] is True
        assert step["_template_id"] == "nav_to_url"

    def test_template_categories(self):
        """Test template categorization."""
        nav_templates = get_templates_by_category("navigation")
        assert len(nav_templates) > 0
        assert "nav_to_url" in nav_templates

        verification_templates = get_templates_by_category("verification")
        assert len(verification_templates) > 0
        assert "verify_element_visible" in verification_templates

    def test_custom_step_creation(self):
        """Test custom step creation."""
        custom_step = create_custom_step_template(
            step_no="1",
            action="custom_action",
            locator_strategy="css",
            locator="#test",
            expected_result="Custom result"
        )

        assert custom_step["step_no"] == "1"
        assert custom_step["action"] == "custom_action"
        assert custom_step["locator_strategy"] == "css"
        assert custom_step["locator"] == "#test"
        assert custom_step["_is_manual"] is True


class TestStepMerger:
    """Test step merging functionality."""

    def test_basic_merging(self):
        """Test basic AI and manual step merging."""
        # Create AI steps
        ai_steps = [
            {
                "step_no": "1",
                "action": "navigate",
                "locator": "https://example.com",
                "expected_result": "Page loads"
            },
            {
                "step_no": "2",
                "action": "click",
                "locator": "#login-btn",
                "expected_result": "Login form appears"
            }
        ]

        # Create manual steps
        insertion_points = {
            "start": [
                {
                    "step_no": "M1",
                    "action": "wait_for_page_load",
                    "expected_result": "Page fully loaded",
                    "_is_manual": True
                }
            ],
            "after_1": [
                {
                    "step_no": "M2",
                    "action": "verify_element_visible",
                    "locator": "#page-title",
                    "expected_result": "Title is visible",
                    "_is_manual": True
                }
            ]
        }

        # Merge steps
        merged_steps, markdown_table, validation_results = merge_ai_and_manual_steps(
            ai_steps, insertion_points
        )

        # Verify merging
        assert len(merged_steps) == 4  # 1 start + 2 AI + 1 after_1
        assert merged_steps[0]["action"] == "wait_for_page_load"  # Manual start
        assert merged_steps[1]["action"] == "navigate"  # AI step 1
        assert merged_steps[2]["action"] == "verify_element_visible"  # Manual after_1
        assert merged_steps[3]["action"] == "click"  # AI step 2

        # Verify step renumbering
        for i, step in enumerate(merged_steps, 1):
            assert step["step_no"] == str(i)

        # Verify markdown generation
        assert "| Step No |" in markdown_table
        assert "🤖 AI" in markdown_table
        assert "✏️ Manual" in markdown_table

    def test_step_merger_class(self):
        """Test StepMerger class functionality."""
        merger = StepMerger()

        # Set AI steps
        ai_steps = [
            {"step_no": "1", "action": "navigate", "expected_result": "Page loads"}
        ]
        merger.set_ai_steps(ai_steps)

        # Add manual step
        manual_step = {
            "action": "wait",
            "expected_result": "Wait completed"
        }
        merger.add_manual_step(manual_step, "start")

        # Merge and verify
        merged = merger.merge_steps()
        assert len(merged) == 2
        assert merged[0]["action"] == "wait"  # Manual step first
        assert merged[1]["action"] == "navigate"  # AI step second

        # Verify AI steps are marked as locked
        assert merged[1]["_is_locked"] is True
        assert merged[0]["_is_locked"] is False


class TestStepValidation:
    """Test step validation functionality."""

    def test_validation_with_good_steps(self):
        """Test validation with well-formed steps."""
        good_steps = [
            {
                "step_no": "1",
                "step_type": "ui",
                "action": "navigate",
                "locator": "https://example.com",
                "expected_result": "Page loads",
                "timeout": 10
            },
            {
                "step_no": "2",
                "step_type": "assertion",
                "action": "verify_element_visible",
                "locator": "#title",
                "expected_result": "Title visible",
                "assertion_type": "element_visible",
                "timeout": 5
            },
            {
                "step_no": "3",
                "step_type": "teardown",
                "action": "close_browser",
                "expected_result": "Browser closed",
                "timeout": 5
            }
        ]

        validation_result = validate_combined_steps(good_steps)
        validation_dict = validation_result.to_dict()

        # Should have minimal issues
        assert validation_dict["is_valid"] is True
        assert len(validation_dict["errors"]) == 0

        # Get summary
        summary = get_validation_summary(validation_result)
        assert "✅" in summary or "No issues found" in summary

    def test_validation_with_problematic_steps(self):
        """Test validation with problematic steps."""
        bad_steps = [
            {
                "step_no": "1",
                "step_type": "teardown",
                "action": "close_browser",
                "expected_result": "Browser closed"
            },
            {
                "step_no": "2",
                "step_type": "ui",
                "action": "click",
                "locator": "#button",
                "expected_result": "Button clicked"
            }
        ]

        validation_result = validate_combined_steps(bad_steps)
        validation_dict = validation_result.to_dict()

        # Should have issues (teardown before UI action)
        assert len(validation_dict["errors"]) > 0 or len(validation_dict["warnings"]) > 0

        # Get summary
        summary = get_validation_summary(validation_result)
        assert "errors" in summary.lower() or "warnings" in summary.lower()


class TestStateManagerIntegration:
    """Test StateManager integration with hybrid editing."""

    def test_hybrid_editing_enable_disable(self):
        """Test enabling and disabling hybrid editing."""
        state = StateManager()

        # Initially disabled
        assert state.hybrid_editing_enabled is False

        # Set up step table
        state.step_table_json = [
            {"step_no": "1", "action": "navigate", "expected_result": "Page loads"}
        ]

        # Enable hybrid editing
        result = state.enable_hybrid_editing()
        assert result is True
        assert state.hybrid_editing_enabled is True
        assert state.ai_generated_steps is not None
        assert len(state.ai_generated_steps) == 1
        assert state.ai_generated_steps[0]["_is_ai_generated"] is True

        # Disable hybrid editing
        state.disable_hybrid_editing()
        assert state.hybrid_editing_enabled is False
        assert len(state.step_insertion_points) == 0

    def test_manual_step_management(self):
        """Test adding and removing manual steps."""
        state = StateManager()
        state.step_table_json = [
            {"step_no": "1", "action": "navigate", "expected_result": "Page loads"}
        ]
        state.enable_hybrid_editing()

        # Add manual step
        manual_step = {
            "action": "wait",
            "expected_result": "Wait completed"
        }
        state.add_manual_step(manual_step, "start")

        # Verify addition
        assert "start" in state.step_insertion_points
        assert len(state.step_insertion_points["start"]) == 1
        assert state.step_insertion_points["start"][0]["action"] == "wait"
        assert state.step_insertion_points["start"][0]["_is_manual"] is True

        # Get combined steps
        combined = state.get_combined_steps()
        assert combined is not None
        assert len(combined) == 2  # 1 manual + 1 AI

    def test_enable_without_step_table(self):
        """Test enabling hybrid editing without step table."""
        state = StateManager()

        # Try to enable without step table
        result = state.enable_hybrid_editing()
        assert result is False
        assert state.hybrid_editing_enabled is False


def test_end_to_end_workflow():
    """Test complete hybrid editing workflow."""
    # 1. Create state with AI-generated steps
    state = StateManager()
    state.step_table_json = [
        {
            "step_no": "1",
            "step_type": "ui",
            "action": "navigate",
            "locator": "https://example.com",
            "expected_result": "Page loads"
        },
        {
            "step_no": "2",
            "step_type": "ui",
            "action": "click",
            "locator": "#login-btn",
            "expected_result": "Login form appears"
        }
    ]

    # 2. Enable hybrid editing
    assert state.enable_hybrid_editing() is True

    # 3. Add manual steps using templates
    nav_template = STEP_TEMPLATES["wait_for_page_load"]
    wait_step = nav_template.create_step("M1")
    state.add_manual_step(wait_step, "after_1")

    verify_template = STEP_TEMPLATES["verify_element_visible"]
    verify_step = verify_template.create_step("M2", {
        "locator": "#page-title",
        "expected_result": "Title is visible"
    })
    state.add_manual_step(verify_step, "end")

    # 4. Get combined steps
    combined_steps = state.get_combined_steps()
    assert combined_steps is not None
    assert len(combined_steps) == 4  # 2 AI + 2 manual

    # 5. Validate combined steps
    validation_result = validate_combined_steps(combined_steps)
    validation_dict = validation_result.to_dict()

    # Should be valid (good flow: navigate -> wait -> click -> verify)
    assert validation_dict["is_valid"] is True

    # 6. Generate markdown
    merger = StepMerger()
    merger.set_ai_steps(state.ai_generated_steps)
    for point, steps in state.step_insertion_points.items():
        for step in steps:
            merger.add_manual_step(step, point)

    markdown = merger.generate_markdown_table(combined_steps)
    assert "| Step No |" in markdown
    assert "🤖 AI" in markdown
    assert "✏️ Manual" in markdown

    print("✅ End-to-end hybrid editing workflow test passed!")


def test_stage_integration():
    """Test that stages properly use effective step table."""
    # Create state with AI-generated steps
    state = StateManager()
    state.step_table_json = [
        {"step_no": "1", "action": "navigate", "expected_result": "Page loads"},
        {"step_no": "2", "action": "click", "expected_result": "Button clicked"}
    ]
    state.total_steps = 2

    # Enable hybrid editing and add manual steps
    assert state.enable_hybrid_editing() is True

    manual_step = {
        "action": "wait_for_page_load",
        "expected_result": "Page fully loaded"
    }
    state.add_manual_step(manual_step, "start")

    # Test effective step table methods
    effective_steps = state.get_effective_step_table()
    print(f"Debug: effective_steps length = {len(effective_steps) if effective_steps else 'None'}")
    print(f"Debug: step_table_json length = {len(state.step_table_json)}")
    print(f"Debug: hybrid_editing_enabled = {state.hybrid_editing_enabled}")
    print(f"Debug: step_insertion_points = {state.step_insertion_points}")

    # The effective step table should return the original steps if combined_step_table is not set
    # We need to get the combined steps first
    combined_steps = state.get_combined_steps()
    print(f"Debug: combined_steps length = {len(combined_steps) if combined_steps else 'None'}")

    assert combined_steps is not None
    assert len(combined_steps) == 3  # 1 manual + 2 AI

    effective_total = state.get_effective_total_steps()
    assert effective_total == 3

    # Test sync functionality
    state.sync_step_table_with_combined()
    assert len(state.step_table_json) == 3  # Should be updated
    assert state.total_steps == 3  # Should be updated

    # Verify step numbering
    for i, step in enumerate(state.step_table_json, 1):
        assert step["step_no"] == str(i)

    # Verify manual step is marked
    assert state.step_table_json[0]["_is_manual"] is True
    assert state.step_table_json[1]["_is_ai_generated"] is True
    assert state.step_table_json[2]["_is_ai_generated"] is True

    print("✅ Stage integration test passed!")


def test_apply_to_test_case_functionality():
    """Test the 'Apply to Test Case' functionality."""
    state = StateManager()
    state.step_table_json = [
        {"step_no": "1", "action": "navigate", "expected_result": "Page loads"}
    ]
    state.total_steps = 1
    state.current_step_index = 0

    # Enable hybrid editing
    assert state.enable_hybrid_editing() is True

    # Add manual step
    manual_step = {
        "action": "verify_title",
        "expected_result": "Title is correct"
    }
    state.add_manual_step(manual_step, "end")

    # Get combined steps
    combined_steps = state.get_combined_steps()
    assert len(combined_steps) == 2

    # Simulate "Apply to Test Case" functionality
    from core.step_merger import merge_ai_and_manual_steps
    merged_steps, markdown_table, _ = merge_ai_and_manual_steps(
        state.ai_generated_steps,
        state.step_insertion_points
    )

    # Apply combined steps (simulate button click)
    state.step_table_json = merged_steps
    state.step_table_markdown = markdown_table
    state.combined_step_table = merged_steps
    state.total_steps = len(merged_steps)
    state.current_step_index = 0
    state.all_steps_done = False

    # Verify application
    assert len(state.step_table_json) == 2
    assert state.total_steps == 2
    assert state.current_step_index == 0
    assert state.all_steps_done is False

    # Verify step content
    assert state.step_table_json[0]["action"] == "navigate"
    assert state.step_table_json[1]["action"] == "verify_title"
    assert state.step_table_json[1]["_is_manual"] is True

    print("✅ Apply to Test Case functionality test passed!")


if __name__ == "__main__":
    # Run tests
    print("Running hybrid editing tests...")

    # Run individual tests
    test_stage_integration()
    test_apply_to_test_case_functionality()
    test_end_to_end_workflow()

    print("\n✅ All manual tests passed!")

    # Run pytest for comprehensive testing
    pytest.main([__file__, "-v"])
