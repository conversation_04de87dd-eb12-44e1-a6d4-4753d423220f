"""
Test script to check if the interactive selector module can be imported correctly.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_interactive_selector")

# Add the parent directory to the path so we can import from parent modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Try to import the interactive selector module
try:
    from core.interactive_selector import select_element_interactively
    logger.info("Successfully imported select_element_interactively from core.interactive_selector")
    print("SUCCESS: Interactive selector module imported correctly")
except ImportError as e:
    logger.error(f"Error importing interactive_selector module: {e}")
    # Print detailed traceback for debugging
    import traceback
    logger.error(f"Traceback: {traceback.format_exc()}")
    print(f"ERROR: Failed to import interactive selector module: {e}")

# Try to import the module directly
try:
    import core.interactive_selector
    logger.info("Successfully imported core.interactive_selector module")
    print("SUCCESS: core.interactive_selector module imported correctly")
except ImportError as e:
    logger.error(f"Error importing core.interactive_selector module: {e}")
    # Print detailed traceback for debugging
    import traceback
    logger.error(f"Traceback: {traceback.format_exc()}")
    print(f"ERROR: Failed to import core.interactive_selector module: {e}")

# Check if the required dependencies are available
try:
    import selenium
    from selenium import webdriver
    logger.info(f"Selenium version: {selenium.__version__}")
    print(f"SUCCESS: Selenium {selenium.__version__} is installed")
except ImportError as e:
    logger.error(f"Error importing selenium: {e}")
    print(f"ERROR: Selenium is not installed: {e}")

try:
    from webdriver_manager.chrome import ChromeDriverManager
    logger.info("Successfully imported ChromeDriverManager")
    print("SUCCESS: webdriver_manager is installed")
except ImportError as e:
    logger.error(f"Error importing ChromeDriverManager: {e}")
    print(f"ERROR: webdriver_manager is not installed: {e}")

print("\nTest completed. Check the logs for details.")
