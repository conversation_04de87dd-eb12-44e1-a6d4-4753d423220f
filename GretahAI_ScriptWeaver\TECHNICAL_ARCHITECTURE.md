# Technical Architecture: Centralized Stage Management System

## Overview

This document provides a detailed technical architecture overview of the centralized stage management system implemented in GretahAI ScriptWeaver, including system diagrams, component interactions, and implementation details.

## System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           GretahAI ScriptWeaver Application                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────────────┐ │
│  │   Streamlit UI  │───▶│   app.py Main    │───▶│     Stage Functions         │ │
│  │   Components    │    │   Controller     │    │   (stages/stage1-8.py)     │ │
│  └─────────────────┘    └──────────────────┘    └─────────────────────────────┘ │
│           │                       │                           │                 │
│           ▼                       ▼                           ▼                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        StateManager (Centralized)                          │ │
│  │  ┌─────────────────┐  ┌──────────────────┐  ┌─────────────────────────────┐ │ │
│  │  │   StageEnum     │  │ advance_to_stage │  │    Flag Cleanup System      │ │ │
│  │  │ (Authoritative) │  │    (Validator)   │  │  (Stale State Prevention)   │ │ │
│  │  └─────────────────┘  └──────────────────┘  └─────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│           │                       │                           │                 │
│           ▼                       ▼                           ▼                 │
│  ┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────────────┐ │
│  │   Logging       │    │   Session State  │    │     Transition History      │ │
│  │   System        │    │   Persistence    │    │      (Debug Support)        │ │
│  └─────────────────┘    └──────────────────┘    └─────────────────────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Component Architecture

### 1. StageEnum - Authoritative Stage Definition

```python
┌─────────────────────────────────────────────────────────────┐
│                        StageEnum                            │
├─────────────────────────────────────────────────────────────┤
│ + STAGE_1: "stage_1" (CSV Upload)                          │
│ + STAGE_2: "stage_2" (Website Configuration)               │
│ + STAGE_3: "stage_3" (Test Case Analysis and Conversion)   │
│ + STAGE_4: "stage_4" (UI Element Detection and Selection)  │
│ + STAGE_5: "stage_5" (Manual Data Entry)                   │
│ + STAGE_6: "stage_6" (Test Script Generation)              │
│ + STAGE_7: "stage_7" (Test Script Execution)               │
│ + STAGE_8: "stage_8" (Script Consolidation & Optimization) │
├─────────────────────────────────────────────────────────────┤
│ + get_stage_number() -> int                                 │
│ + get_display_name() -> str                                 │
│ + from_number(num: int) -> StageEnum                        │
└─────────────────────────────────────────────────────────────┘
```

### 2. StateManager - Centralized State Control

```python
┌─────────────────────────────────────────────────────────────┐
│                      StateManager                          │
├─────────────────────────────────────────────────────────────┤
│ Fields:                                                     │
│ + current_stage: StageEnum = STAGE_1                        │
│ + test_cases: List[Dict]                                    │
│ + selected_test_case: Optional[Dict]                        │
│ + conversion_done: bool = False                             │
│ + step_table_json: Optional[List[Dict]]                     │
│ + selected_step: Optional[Dict]                             │
│ + step_matches: Dict[str, Any] = {}                         │
│ + test_data: Dict[str, Any] = {}                            │
│ + generated_script_path: Optional[str]                      │
│ + all_steps_done: bool = False                              │
├─────────────────────────────────────────────────────────────┤
│ Methods:                                                    │
│ + advance_to_stage(target: StageEnum, reason: str) -> bool  │
│ + update_stage_based_on_completion() -> bool               │
│ + reset_test_case_state(confirm: bool, reason: str) -> bool │
│ + reset_step_state(confirm: bool, reason: str) -> bool      │
│ + _cleanup_flags_for_stage_transition(...)                 │
│ + current_app_stage -> int (backward compatibility)        │
└─────────────────────────────────────────────────────────────┘
```

## Stage Transition Flow Diagram

```
                    ┌─────────────────┐
                    │   User Action   │
                    └─────────┬───────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │ Stage Function  │
                    │ (stage1-8.py)   │
                    └─────────┬───────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │advance_to_stage │
                    │   (Validator)   │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │ Transition      │
                    │ Validation      │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐     ┌─────────────────┐
                    │ Legal?          │────▶│ Reject & Log    │
                    │ (Check Rules)   │ No  │ Error Message   │
                    └─────────┬───────┘     └─────────────────┘
                              │ Yes
                              ▼
                    ┌─────────────────┐
                    │ Update Current  │
                    │ Stage (Atomic)  │
                    └─────────┬───────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │ Flag Cleanup    │
                    │ (Prevent Stale) │
                    └─────────┬───────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │ Log Transition  │
                    │ (Debug Support) │
                    └─────────┬───────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │ Update Session  │
                    │ State & Rerun   │
                    └─────────────────┘
```

## Transition Validation Logic

### Legal Transition Rules

```python
┌─────────────────────────────────────────────────────────────┐
│                 Transition Validation Matrix               │
├─────────────────────────────────────────────────────────────┤
│ From Stage │ Legal Forward │ Legal Backward │ Special Rules │
├────────────┼───────────────┼────────────────┼───────────────┤
│ STAGE_1    │ 2,3,4,5,6,7,8 │ None           │ Always allow  │
│ STAGE_2    │ 3,4,5,6,7,8   │ 1 (reset only) │ Forward only  │
│ STAGE_3    │ 4,5,6,7,8     │ 1 (reset only) │ Forward only  │
│ STAGE_4    │ 5,6,7,8       │ 1 (reset only) │ Forward only  │
│ STAGE_5    │ 6,7,8         │ 1 (reset only) │ Forward only  │
│ STAGE_6    │ 7,8           │ 1 (reset only) │ Forward only  │
│ STAGE_7    │ 8             │ 4 (step loop)  │ Special case  │
│ STAGE_8    │ None          │ 3 (new case)   │ Special case  │
└────────────┴───────────────┴────────────────┴───────────────┘
```

### Validation Algorithm

```python
def validate_transition(current_stage: StageEnum, target_stage: StageEnum) -> bool:
    """
    Validation algorithm for stage transitions
    """
    current_num = current_stage.get_stage_number()
    target_num = target_stage.get_stage_number()
    
    # Forward transitions are generally allowed
    if target_num > current_num:
        return True
    
    # Complete reset to Stage 1 is always allowed
    if target_num == 1:
        return True
    
    # Check legal backward transitions
    legal_backward = LEGAL_BACKWARD_TRANSITIONS.get(current_stage, [])
    return target_stage in legal_backward
```

## Flag Cleanup Architecture

### Cleanup Strategy Matrix

```python
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           Flag Cleanup Strategy                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│ Target Stage │ Cleanup Level │ Flags Cleared                                   │
├──────────────┼───────────────┼─────────────────────────────────────────────────┤
│ STAGE_1      │ Complete      │ All flags (full application reset)             │
│ STAGE_2      │ Partial       │ Test case and step flags                       │
│ STAGE_3      │ Test Case     │ conversion_done, step_*, test_data, scripts    │
│ STAGE_4      │ Step Level    │ step_matches, test_data, generated_script_path │
│ STAGE_5      │ Data Level    │ test_data, generated_script_path               │
│ STAGE_6      │ Script Level  │ generated_script_path                          │
│ STAGE_7      │ Execution     │ execution_*, validation_*                      │
│ STAGE_8      │ Optimization  │ optimization_*, optimized_script_*             │
└──────────────┴───────────────┴─────────────────────────────────────────────────┘
```

### Cleanup Implementation

```python
def _cleanup_flags_for_stage_transition(self, previous_stage, target_stage, reason):
    """
    Intelligent flag cleanup based on transition type
    """
    cleanup_map = {
        StageEnum.STAGE_1: self._cleanup_complete_reset,
        StageEnum.STAGE_3: self._cleanup_test_case_level,
        StageEnum.STAGE_4: self._cleanup_step_level,
        StageEnum.STAGE_6: self._cleanup_script_level,
    }
    
    cleanup_func = cleanup_map.get(target_stage)
    if cleanup_func:
        cleanup_func(reason)
```

## Logging and Debugging Architecture

### Logging Hierarchy

```
┌─────────────────────────────────────────────────────────────┐
│                    Logging Architecture                    │
├─────────────────────────────────────────────────────────────┤
│ Logger: ScriptWeaver.state_manager                          │
│ ├── INFO: Stage transitions                                 │
│ ├── INFO: State changes (with before/after values)         │
│ ├── DEBUG: Validation checks                               │
│ ├── WARNING: Fallback operations                           │
│ └── ERROR: Illegal transitions                             │
├─────────────────────────────────────────────────────────────┤
│ Log Format:                                                 │
│ [TIMESTAMP] - [LOGGER] - [LEVEL] - [MESSAGE]               │
│                                                             │
│ Example:                                                    │
│ 2025-05-28 14:02:51,162 - ScriptWeaver.state_manager -     │
│ INFO - Stage transition: CSV Upload -> Website Config      │
└─────────────────────────────────────────────────────────────┘
```

### Debug Information Structure

```python
┌─────────────────────────────────────────────────────────────┐
│                   Debug Information                        │
├─────────────────────────────────────────────────────────────┤
│ Transition Log Entry:                                       │
│ {                                                           │
│   "timestamp": "2025-05-28T14:02:51.162Z",                │
│   "from_stage": "CSV Upload",                              │
│   "to_stage": "Website Configuration",                     │
│   "reason": "User uploaded test cases",                    │
│   "validation_result": "ALLOWED",                          │
│   "flags_cleared": ["conversion_done", "step_matches"],    │
│   "session_id": "abc123"                                   │
│ }                                                           │
└─────────────────────────────────────────────────────────────┘
```

## Performance Characteristics

### Time Complexity Analysis

| Operation | Old System | New System | Improvement |
|-----------|------------|------------|-------------|
| Stage Detection | O(n) | O(1) | n times faster |
| Transition Validation | O(1) | O(1) | Same |
| Flag Cleanup | O(n) | O(1) | n times faster |
| State Persistence | O(n) | O(1) | n times faster |

### Memory Usage

```
┌─────────────────────────────────────────────────────────────┐
│                    Memory Usage Analysis                   │
├─────────────────────────────────────────────────────────────┤
│ Component          │ Memory Usage │ Growth Rate │ Notes      │
├────────────────────┼──────────────┼─────────────┼────────────┤
│ StageEnum          │ ~200 bytes   │ O(1)        │ Static     │
│ StateManager       │ ~2-5 KB      │ O(n)        │ Per state  │
│ Transition History │ ~100 bytes   │ O(t)        │ Per trans. │
│ Logging Buffer     │ ~1-10 KB     │ O(l)        │ Per log    │
└────────────────────┴──────────────┴─────────────┴────────────┘
```

## Integration Points

### Application Integration

```python
┌─────────────────────────────────────────────────────────────┐
│                  Integration Architecture                  │
├─────────────────────────────────────────────────────────────┤
│ app.py (Main Controller)                                    │
│ ├── _get_current_stage_number(state) -> int                 │
│ ├── _display_workflow_summary_sidebar(state)               │
│ └── run_app() -> Main application loop                     │
│                                                             │
│ stages/stage*.py (Stage Functions)                          │
│ ├── stage1_upload_excel(state)                             │
│ ├── stage2_enter_website(state)                            │
│ ├── ...                                                    │
│ └── stage8_optimize_script(state)                          │
│                                                             │
│ state_manager.py (Core System)                             │
│ ├── StageEnum (Authoritative definitions)                  │
│ ├── StateManager (Centralized state)                       │
│ └── Transition methods (Validation & cleanup)              │
└─────────────────────────────────────────────────────────────┘
```

### Backward Compatibility Layer

```python
┌─────────────────────────────────────────────────────────────┐
│                Backward Compatibility                      │
├─────────────────────────────────────────────────────────────┤
│ Legacy Property:                                            │
│ @property                                                   │
│ def current_app_stage(self) -> int:                         │
│     return self.current_stage.get_stage_number()           │
│                                                             │
│ Migration Support:                                          │
│ - Existing code using current_app_stage continues to work  │
│ - Gradual migration to new advance_to_stage() method       │
│ - Deprecation warnings for old patterns                    │
└─────────────────────────────────────────────────────────────┘
```

## Error Handling and Recovery

### Error Scenarios and Recovery

```python
┌─────────────────────────────────────────────────────────────┐
│                   Error Handling Matrix                    │
├─────────────────────────────────────────────────────────────┤
│ Error Type         │ Detection │ Recovery Strategy          │
├────────────────────┼───────────┼────────────────────────────┤
│ Illegal Transition │ Validator │ Reject & log error         │
│ Missing Stage      │ Fallback  │ Initialize to Stage 1      │
│ Corrupted State    │ Validator │ Reset to last known good   │
│ Session Timeout    │ Detector  │ Reinitialize state         │
│ Concurrent Access  │ Lock      │ Serialize access           │
└────────────────────┴───────────┴────────────────────────────┘
```

## Testing Architecture

### Test Coverage Strategy

```python
┌─────────────────────────────────────────────────────────────┐
│                    Test Architecture                       │
├─────────────────────────────────────────────────────────────┤
│ Unit Tests:                                                 │
│ ├── test_stage_enum_functionality()                         │
│ ├── test_state_manager_initialization()                     │
│ ├── test_stage_transition_validation()                      │
│ ├── test_flag_cleanup_system()                             │
│ └── test_backward_compatibility()                          │
│                                                             │
│ Integration Tests:                                          │
│ ├── test_complete_workflow_flow()                          │
│ ├── test_phantom_jump_prevention()                         │
│ ├── test_error_recovery()                                  │
│ └── test_performance_characteristics()                     │
│                                                             │
│ End-to-End Tests:                                           │
│ ├── test_streamlit_application_startup()                   │
│ ├── test_user_workflow_scenarios()                         │
│ └── test_session_persistence()                             │
└─────────────────────────────────────────────────────────────┘
```

## Conclusion

The centralized stage management system provides a robust, scalable, and maintainable architecture for workflow control in GretahAI ScriptWeaver. The technical implementation ensures:

1. **Deterministic behavior** through authoritative stage control
2. **Comprehensive validation** preventing illegal transitions
3. **Intelligent cleanup** eliminating stale state issues
4. **Extensive logging** supporting debugging and monitoring
5. **Backward compatibility** ensuring smooth migration
6. **Performance optimization** with O(1) operations
7. **Comprehensive testing** validating all scenarios

This architecture provides a solid foundation for future enhancements while maintaining the stability and reliability required for production use.
