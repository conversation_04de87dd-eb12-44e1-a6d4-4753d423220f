#!/usr/bin/env python3
"""
Test script to verify that the nested expander issue has been fixed.

This script checks the code for potential nested expander issues by:
1. Parsing the stages_additional.py file
2. Looking for st.expander() calls
3. Checking if any functions that create expanders are called within expander contexts
4. Reporting any potential nesting issues
"""

import ast
import os
import sys

def find_expander_calls(node, function_name=""):
    """Find all st.expander() calls in an AST node."""
    expander_calls = []

    for child in ast.walk(node):
        if isinstance(child, ast.Call):
            # Check for st.expander() calls
            if (isinstance(child.func, ast.Attribute) and
                isinstance(child.func.value, ast.Name) and
                child.func.value.id == 'st' and
                child.func.attr == 'expander'):

                # Get the expander title if available
                title = "Unknown"
                if child.args and isinstance(child.args[0], ast.Constant):
                    title = child.args[0].value

                expander_calls.append({
                    'line': child.lineno,
                    'title': title,
                    'function': function_name
                })

    return expander_calls

def find_functions_that_create_expanders(content):
    """Find functions that create st.expander() calls."""
    functions_with_expanders = set()

    try:
        tree = ast.parse(content)
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                function_name = node.name
                # Check if this function contains st.expander() calls
                for child in ast.walk(node):
                    if (isinstance(child, ast.Call) and
                        isinstance(child.func, ast.Attribute) and
                        isinstance(child.func.value, ast.Name) and
                        child.func.value.id == 'st' and
                        child.func.attr == 'expander'):
                        functions_with_expanders.add(function_name)
                        break
    except SyntaxError:
        pass

    return functions_with_expanders

def find_function_calls_in_expander_context(node, function_name="", functions_with_expanders=None):
    """Find function calls that create expanders within with st.expander contexts."""
    if functions_with_expanders is None:
        functions_with_expanders = set()

    potential_issues = []

    for child in ast.walk(node):
        if isinstance(child, ast.With):
            # Check if this is a with st.expander() context
            for item in child.items:
                if (isinstance(item.context_expr, ast.Call) and
                    isinstance(item.context_expr.func, ast.Attribute) and
                    isinstance(item.context_expr.func.value, ast.Name) and
                    item.context_expr.func.value.id == 'st' and
                    item.context_expr.func.attr == 'expander'):

                    # Look for function calls within this expander context
                    for body_node in ast.walk(child):
                        if isinstance(body_node, ast.Call) and isinstance(body_node.func, ast.Name):
                            func_name = body_node.func.id
                            # Only report functions that actually create expanders
                            if func_name in functions_with_expanders:
                                potential_issues.append({
                                    'line': body_node.lineno,
                                    'function_called': func_name,
                                    'within_function': function_name,
                                    'expander_line': item.context_expr.lineno
                                })

    return potential_issues

def analyze_file(file_path):
    """Analyze a Python file for nested expander issues."""
    print(f"Analyzing {file_path}...")

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    try:
        tree = ast.parse(content)
    except SyntaxError as e:
        print(f"Syntax error in {file_path}: {e}")
        return

    # First, find all functions that create expanders
    functions_with_expanders = find_functions_that_create_expanders(content)
    print(f"\n=== FUNCTIONS THAT CREATE EXPANDERS ===")
    for func in sorted(functions_with_expanders):
        print(f"- {func}")

    all_expander_calls = []
    all_potential_issues = []

    # Analyze each function definition
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            function_name = node.name

            # Find expander calls in this function
            expander_calls = find_expander_calls(node, function_name)
            all_expander_calls.extend(expander_calls)

            # Find potential nesting issues
            potential_issues = find_function_calls_in_expander_context(node, function_name, functions_with_expanders)
            all_potential_issues.extend(potential_issues)

    # Report findings
    print(f"\n=== EXPANDER CALLS FOUND ===")
    for call in all_expander_calls:
        print(f"Line {call['line']}: st.expander('{call['title']}') in function {call['function']}")

    print(f"\n=== POTENTIAL NESTING ISSUES ===")
    if all_potential_issues:
        for issue in all_potential_issues:
            print(f"Line {issue['line']}: Function '{issue['function_called']}' called within expander context (expander at line {issue['expander_line']}) in function '{issue['within_function']}'")
    else:
        print("No potential nesting issues found!")

    return len(all_potential_issues) == 0

def main():
    """Main function to run the analysis."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    stages_file = os.path.join(script_dir, 'stages_additional.py')

    if not os.path.exists(stages_file):
        print(f"Error: {stages_file} not found!")
        return False

    success = analyze_file(stages_file)

    if success:
        print("\n✅ SUCCESS: No nested expander issues detected!")
        return True
    else:
        print("\n❌ ISSUES FOUND: Potential nested expander problems detected!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
