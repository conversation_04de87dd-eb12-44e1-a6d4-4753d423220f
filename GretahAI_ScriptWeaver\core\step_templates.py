"""
Step Templates for Hybrid AI-Assisted Test Case Editing

This module provides predefined step templates that users can insert into their test cases
to supplement AI-generated steps with common manual additions like navigation, verification,
and wait steps.

Key Features:
- Predefined templates for common test step types
- Customizable templates with placeholder values
- Integration with the hybrid editing system
- Consistent step format matching AI-generated steps
"""

from typing import Dict, List, Any, Optional
from datetime import datetime


class StepTemplateCategory:
    """Categories for organizing step templates."""
    NAVIGATION = "navigation"
    VERIFICATION = "verification"
    WAIT = "wait"
    DATA_SETUP = "data_setup"
    ERROR_HANDLING = "error_handling"
    CLEANUP = "cleanup"
    CUSTOM = "custom"


class StepTemplate:
    """Represents a step template with metadata and customization options."""
    
    def __init__(self, template_id: str, name: str, category: str, description: str, 
                 step_data: Dict[str, Any], customizable_fields: List[str] = None):
        self.template_id = template_id
        self.name = name
        self.category = category
        self.description = description
        self.step_data = step_data
        self.customizable_fields = customizable_fields or []
    
    def create_step(self, step_no: str, customizations: Dict[str, str] = None) -> Dict[str, Any]:
        """
        Create a step instance from this template.
        
        Args:
            step_no: Step number for the new step
            customizations: Dictionary of field customizations
            
        Returns:
            Dict containing the step data
        """
        step = self.step_data.copy()
        step["step_no"] = step_no
        step["_template_id"] = self.template_id
        step["_is_manual"] = True
        step["_created_at"] = datetime.now().isoformat()
        
        # Apply customizations
        if customizations:
            for field, value in customizations.items():
                if field in self.customizable_fields and value:
                    step[field] = value
        
        return step


# Predefined step templates
STEP_TEMPLATES = {
    # Navigation Templates
    "nav_to_url": StepTemplate(
        template_id="nav_to_url",
        name="Navigate to URL",
        category=StepTemplateCategory.NAVIGATION,
        description="Navigate to a specific URL",
        step_data={
            "step_type": "ui",
            "action": "navigate",
            "locator_strategy": "url",
            "locator": "https://example.com",
            "test_data_param": "",
            "expected_result": "Page loads successfully",
            "assertion_type": "page_title",
            "condition": "",
            "timeout": 10
        },
        customizable_fields=["locator", "expected_result", "timeout"]
    ),
    
    "nav_back": StepTemplate(
        template_id="nav_back",
        name="Navigate Back",
        category=StepTemplateCategory.NAVIGATION,
        description="Navigate back to previous page",
        step_data={
            "step_type": "ui",
            "action": "navigate_back",
            "locator_strategy": "",
            "locator": "",
            "test_data_param": "",
            "expected_result": "Previous page loads",
            "assertion_type": "url_contains",
            "condition": "",
            "timeout": 10
        },
        customizable_fields=["expected_result", "timeout"]
    ),
    
    "refresh_page": StepTemplate(
        template_id="refresh_page",
        name="Refresh Page",
        category=StepTemplateCategory.NAVIGATION,
        description="Refresh the current page",
        step_data={
            "step_type": "ui",
            "action": "refresh",
            "locator_strategy": "",
            "locator": "",
            "test_data_param": "",
            "expected_result": "Page refreshes successfully",
            "assertion_type": "page_loaded",
            "condition": "",
            "timeout": 10
        },
        customizable_fields=["expected_result", "timeout"]
    ),
    
    # Verification Templates
    "verify_element_visible": StepTemplate(
        template_id="verify_element_visible",
        name="Verify Element Visible",
        category=StepTemplateCategory.VERIFICATION,
        description="Verify that an element is visible on the page",
        step_data={
            "step_type": "assertion",
            "action": "verify_element_visible",
            "locator_strategy": "css",
            "locator": "#element-id",
            "test_data_param": "",
            "expected_result": "Element is visible",
            "assertion_type": "element_visible",
            "condition": "",
            "timeout": 10
        },
        customizable_fields=["locator_strategy", "locator", "expected_result", "timeout"]
    ),
    
    "verify_text_content": StepTemplate(
        template_id="verify_text_content",
        name="Verify Text Content",
        category=StepTemplateCategory.VERIFICATION,
        description="Verify that an element contains specific text",
        step_data={
            "step_type": "assertion",
            "action": "verify_text",
            "locator_strategy": "css",
            "locator": "#element-id",
            "test_data_param": "Expected Text",
            "expected_result": "Text matches expected value",
            "assertion_type": "text_equals",
            "condition": "",
            "timeout": 10
        },
        customizable_fields=["locator_strategy", "locator", "test_data_param", "expected_result", "timeout"]
    ),
    
    "verify_url": StepTemplate(
        template_id="verify_url",
        name="Verify Current URL",
        category=StepTemplateCategory.VERIFICATION,
        description="Verify that the current URL matches expected value",
        step_data={
            "step_type": "assertion",
            "action": "verify_url",
            "locator_strategy": "",
            "locator": "",
            "test_data_param": "https://expected-url.com",
            "expected_result": "URL matches expected value",
            "assertion_type": "url_equals",
            "condition": "",
            "timeout": 5
        },
        customizable_fields=["test_data_param", "expected_result", "timeout"]
    ),
    
    # Wait Templates
    "wait_for_element": StepTemplate(
        template_id="wait_for_element",
        name="Wait for Element",
        category=StepTemplateCategory.WAIT,
        description="Wait for an element to become visible",
        step_data={
            "step_type": "ui",
            "action": "wait_for_element",
            "locator_strategy": "css",
            "locator": "#element-id",
            "test_data_param": "",
            "expected_result": "Element becomes visible",
            "assertion_type": "element_visible",
            "condition": "",
            "timeout": 30
        },
        customizable_fields=["locator_strategy", "locator", "expected_result", "timeout"]
    ),
    
    "wait_for_page_load": StepTemplate(
        template_id="wait_for_page_load",
        name="Wait for Page Load",
        category=StepTemplateCategory.WAIT,
        description="Wait for page to fully load",
        step_data={
            "step_type": "ui",
            "action": "wait_for_page_load",
            "locator_strategy": "",
            "locator": "",
            "test_data_param": "",
            "expected_result": "Page loads completely",
            "assertion_type": "page_loaded",
            "condition": "",
            "timeout": 30
        },
        customizable_fields=["expected_result", "timeout"]
    ),
    
    "wait_seconds": StepTemplate(
        template_id="wait_seconds",
        name="Wait (Fixed Duration)",
        category=StepTemplateCategory.WAIT,
        description="Wait for a fixed number of seconds",
        step_data={
            "step_type": "ui",
            "action": "wait",
            "locator_strategy": "",
            "locator": "",
            "test_data_param": "5",
            "expected_result": "Wait completed",
            "assertion_type": "no_error",
            "condition": "",
            "timeout": 60
        },
        customizable_fields=["test_data_param", "timeout"]
    ),
    
    # Data Setup Templates
    "clear_input_field": StepTemplate(
        template_id="clear_input_field",
        name="Clear Input Field",
        category=StepTemplateCategory.DATA_SETUP,
        description="Clear the content of an input field",
        step_data={
            "step_type": "ui",
            "action": "clear",
            "locator_strategy": "css",
            "locator": "#input-field",
            "test_data_param": "",
            "expected_result": "Field is cleared",
            "assertion_type": "element_value",
            "condition": "",
            "timeout": 10
        },
        customizable_fields=["locator_strategy", "locator", "expected_result", "timeout"]
    ),
    
    # Error Handling Templates
    "handle_alert": StepTemplate(
        template_id="handle_alert",
        name="Handle Alert Dialog",
        category=StepTemplateCategory.ERROR_HANDLING,
        description="Accept or dismiss browser alert dialog",
        step_data={
            "step_type": "ui",
            "action": "handle_alert",
            "locator_strategy": "",
            "locator": "",
            "test_data_param": "accept",
            "expected_result": "Alert handled successfully",
            "assertion_type": "no_error",
            "condition": "",
            "timeout": 10
        },
        customizable_fields=["test_data_param", "expected_result", "timeout"]
    ),
    
    # Cleanup Templates
    "close_browser": StepTemplate(
        template_id="close_browser",
        name="Close Browser",
        category=StepTemplateCategory.CLEANUP,
        description="Close the browser window",
        step_data={
            "step_type": "teardown",
            "action": "close_browser",
            "locator_strategy": "",
            "locator": "",
            "test_data_param": "",
            "expected_result": "Browser closed",
            "assertion_type": "no_error",
            "condition": "",
            "timeout": 5
        },
        customizable_fields=["expected_result", "timeout"]
    )
}


def get_templates_by_category(category: str = None) -> Dict[str, StepTemplate]:
    """
    Get step templates filtered by category.
    
    Args:
        category: Category to filter by, or None for all templates
        
    Returns:
        Dictionary of template_id -> StepTemplate
    """
    if category is None:
        return STEP_TEMPLATES
    
    return {
        template_id: template 
        for template_id, template in STEP_TEMPLATES.items() 
        if template.category == category
    }


def get_template_categories() -> List[str]:
    """Get list of all available template categories."""
    categories = set()
    for template in STEP_TEMPLATES.values():
        categories.add(template.category)
    return sorted(list(categories))


def create_custom_step_template(step_no: str, action: str, locator_strategy: str = "", 
                               locator: str = "", test_data_param: str = "", 
                               expected_result: str = "", assertion_type: str = "no_error",
                               timeout: int = 10) -> Dict[str, Any]:
    """
    Create a custom step template with user-provided values.
    
    Args:
        step_no: Step number
        action: Action to perform
        locator_strategy: Strategy for locating elements
        locator: Element locator
        test_data_param: Test data parameter
        expected_result: Expected result
        assertion_type: Type of assertion
        timeout: Timeout in seconds
        
    Returns:
        Dictionary containing the custom step data
    """
    return {
        "step_no": step_no,
        "step_type": "ui",
        "action": action,
        "locator_strategy": locator_strategy,
        "locator": locator,
        "test_data_param": test_data_param,
        "expected_result": expected_result,
        "assertion_type": assertion_type,
        "condition": "",
        "timeout": timeout,
        "_template_id": "custom",
        "_is_manual": True,
        "_created_at": datetime.now().isoformat()
    }
