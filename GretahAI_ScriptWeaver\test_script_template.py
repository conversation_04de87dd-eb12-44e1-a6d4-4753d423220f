"""
Test Script Template

This module provides a template for generating test scripts from Excel test cases.
"""

import pytest
import time
import logging
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementNotInteractableException

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Page Object Base Class
class BasePage:
    def __init__(self, driver):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)

    def find_element(self, locator):
        return self.wait.until(EC.presence_of_element_located(locator))

    def find_clickable_element(self, locator):
        return self.wait.until(EC.element_to_be_clickable(locator))

    def find_visible_element(self, locator):
        return self.wait.until(EC.visibility_of_element_located(locator))

@pytest.fixture
def driver():
    logger.info('Setting up WebDriver')
    chrome_options = Options()

    # Check if headless mode is enabled via environment variable
    if os.environ.get('HEADLESS') == '1':
        logger.info('Running in headless mode')
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')

    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()

    # Set up screenshot directory
    os.makedirs('screenshots', exist_ok=True)

    yield driver
    logger.info('Tearing down WebDriver')
    driver.quit()

class TestHelper:
    @staticmethod
    def wait_for_element(driver, locator, timeout=10, condition=EC.presence_of_element_located):
        """Wait for an element and return it when found."""
        try:
            element = WebDriverWait(driver, timeout).until(condition(locator))
            return element
        except TimeoutException:
            logger.error(f'Element not found with locator: {locator}')
            screenshot_path = f'screenshots/error_element_not_found_{time.strftime("%Y%m%d_%H%M%S")}.png'
            driver.save_screenshot(screenshot_path)
            logger.info(f'Screenshot saved to {screenshot_path}')
            raise

    @staticmethod
    def input_text(driver, locator, text, clear=True):
        """Input text into an element."""
        element = TestHelper.wait_for_element(driver, locator)
        try:
            if clear:
                element.clear()
            element.send_keys(text)
            logger.info(f'Entered text: {text} into element: {locator}')
            return True
        except (ElementNotInteractableException, NoSuchElementException) as e:
            logger.error(f'Failed to input text: {text} into element: {locator}. Error: {str(e)}')
            screenshot_path = f'screenshots/error_input_text_{time.strftime("%Y%m%d_%H%M%S")}.png'
            driver.save_screenshot(screenshot_path)
            logger.info(f'Screenshot saved to {screenshot_path}')
            raise

    @staticmethod
    def click_element(driver, locator):
        """Click on an element."""
        element = TestHelper.wait_for_element(driver, locator, condition=EC.element_to_be_clickable)
        try:
            element.click()
            logger.info(f'Clicked element: {locator}')
            return True
        except (ElementNotInteractableException, NoSuchElementException) as e:
            logger.error(f'Failed to click element: {locator}. Error: {str(e)}')
            screenshot_path = f'screenshots/error_click_{time.strftime("%Y%m%d_%H%M%S")}.png'
            driver.save_screenshot(screenshot_path)
            logger.info(f'Screenshot saved to {screenshot_path}')
            raise

    @staticmethod
    def verify_element_displayed(driver, locator):
        """Verify that an element is displayed."""
        try:
            element = TestHelper.wait_for_element(driver, locator, condition=EC.visibility_of_element_located)
            assert element.is_displayed(), f'Element {locator} is not displayed'
            logger.info(f'Element is displayed: {locator}')
            return True
        except (TimeoutException, AssertionError) as e:
            logger.error(f'Element not displayed: {locator}. Error: {str(e)}')
            screenshot_path = f'screenshots/error_not_displayed_{time.strftime("%Y%m%d_%H%M%S")}.png'
            driver.save_screenshot(screenshot_path)
            logger.info(f'Screenshot saved to {screenshot_path}')
            raise

    @staticmethod
    def verify_url_contains(driver, text, timeout=10):
        """Verify that the URL contains specific text."""
        try:
            WebDriverWait(driver, timeout).until(EC.url_contains(text))
            logger.info(f'URL contains: {text}')
            return True
        except TimeoutException:
            logger.error(f'URL does not contain: {text}. Current URL: {driver.current_url}')
            screenshot_path = f'screenshots/error_url_not_contains_{time.strftime("%Y%m%d_%H%M%S")}.png'
            driver.save_screenshot(screenshot_path)
            logger.info(f'Screenshot saved to {screenshot_path}')
            raise AssertionError(f'URL does not contain: {text}. Current URL: {driver.current_url}')

# Test Data
TEST_DATA = {
    'valid_email': '<EMAIL>',
    'valid_password': 'Password123!',
    'valid_mobile': '1234567890',
    'invalid_email': 'invalid@example',
    'invalid_password': 'short',
    'invalid_mobile': '123',
}

# Element Locators
class Locators:
    """
    This class will be populated with element locators from the AI element matching.
    The locators below are just examples and will be replaced with the actual matched elements.
    """
    # Common locators
    EMAIL_INPUT = (By.ID, 'email')
    PASSWORD_INPUT = (By.ID, 'password')
    MOBILE_INPUT = (By.ID, 'telephone')
    LOGIN_BUTTON = (By.ID, 'btnLogin')
    ERROR_MESSAGE = (By.CLASS_NAME, 'error-message')

# Page Objects
class LoginPage(BasePage):
    def enter_email(self, email):
        self.find_element(Locators.EMAIL_INPUT).clear()
        self.find_element(Locators.EMAIL_INPUT).send_keys(email)

    def enter_password(self, password):
        self.find_element(Locators.PASSWORD_INPUT).clear()
        self.find_element(Locators.PASSWORD_INPUT).send_keys(password)

    def click_login_button(self):
        self.find_clickable_element(Locators.LOGIN_BUTTON).click()

    def get_error_message(self):
        return self.find_visible_element(Locators.ERROR_MESSAGE).text

# Test Case Template
class TestCaseTemplate:
    """
    Template for test cases.

    This template will be replaced with actual test cases generated from your Excel file.
    Each test case will use the matched elements from the AI analysis step and the test data
    (either AI-generated or manually entered).
    """

    def test_template(self, driver):
        """Template test case."""
        logger.info('Starting test case: Template')

        # Navigate to the URL
        driver.get('https://example.com')
        logger.info('Navigated to URL: https://example.com')

        # Wait for page to load
        time.sleep(2)

        try:
            # Example of using test data and matched elements
            logger.info('Example test steps:')

            # Login example
            if hasattr(Locators, 'EMAIL_INPUT') and 'valid_email' in TEST_DATA:
                logger.info(f"Step 1: Enter email: {TEST_DATA['valid_email']}")
                TestHelper.input_text(driver, Locators.EMAIL_INPUT, TEST_DATA['valid_email'])

            if hasattr(Locators, 'PASSWORD_INPUT') and 'valid_password' in TEST_DATA:
                logger.info(f"Step 2: Enter password: {TEST_DATA['valid_password']}")
                TestHelper.input_text(driver, Locators.PASSWORD_INPUT, TEST_DATA['valid_password'])

            if hasattr(Locators, 'LOGIN_BUTTON'):
                logger.info("Step 3: Click login button")
                TestHelper.click_element(driver, Locators.LOGIN_BUTTON)

            # Verification example
            logger.info("Step 4: Verify successful login")
            TestHelper.verify_url_contains(driver, 'dashboard')

            logger.info('Test case completed successfully')
        except Exception as e:
            logger.error(f'Test case failed: {str(e)}')
            screenshot_path = f'screenshots/test_failure_{time.strftime("%Y%m%d_%H%M%S")}.png'
            driver.save_screenshot(screenshot_path)
            logger.info(f'Screenshot saved to {screenshot_path}')
            raise
