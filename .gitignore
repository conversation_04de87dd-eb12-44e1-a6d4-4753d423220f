# Ignore config file with sensitive information
config.json
admin_config.json
upload_config.json
google_usage_stats.json
*.db
*.db-shm
*.db-wal

# Generated test scenario files
test_scenarios_*.xlsx
Test_cases_*.xlsx

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
venv/
env/
.env
.venv
ENV/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints

# Streamlit
.streamlit/secrets.toml
.streamlit/

# Project specific
logs/
screenshots/
uploads/
*.log
*.db-shm
*.db-wal
temp_results.xml
raw_outputs/
*.summary.txt
rca_history.json
rca_analysis_*.txt
test_history.json
test_cases_v2.db.corrupted

# OS specific
Thumbs.db
.directory


# New structure with GretahAI_CaseForge directory
GretahAI_CaseForge/gui/Test_cases/
GretahAI_CaseForge/gui/attached_images/
GretahAI_CaseForge/gui/raw_responses/
GretahAI_CaseForge/gui/usage_data.json
GretahAI_CaseForge/gui/edited_excel/
GretahAI_CaseForge/gui/csv_exports/

# Autotest directories
Autotest/logs/*
Autotest/screenshots/*
Autotest/test_results.db

# Ignore specific notebooks
Autotest/ind_contribution.ipynb

# Auto test generator
auto_test_generator/detected_elements/*
auto_test_generator/generated_tests/*
auto_test_generator/temp_uploads/*

# Backup and temporary files
test_cases_v2.db.back*
results_*.xml

# Fix scripts (keep these in version control)
# run_caseforge.py
# test_and_fix_database.py
# fix_jira_ids.py
# fix_test_cases_query.py
GretahAI_CaseForge/usage_data.json

GretahAI_CaseForge/Test_cases/*
GretahAI_CaseForge/edited_excel/*
GretahAI_CaseForge/gui/temp_excel/*
GretahAI_CaseForge/raw_responses/*
GretahAI_CaseForge/attached_images/*
GretahAI_CaseForge/temp_excel/*

Gretah/*

GretahAI_TestInsight/reports/*
GretahAI_ScriptWeaver/generated_tests/*
GretahAI_ScriptWeaver/temp_uploads/*
GretahAI_ScriptWeaver/ai_logs/*
temp_uploads/*
generated_tests/*
GretahAI_ScriptWeaver/test_stage8_fixes.py
GretahAI_ScriptWeaver/STAGE8_FIXES_SUMMARY.md
GretahAI_ScriptWeaver/page_sources/*

GretahAI_ScriptWeaver/test_data_runtime.json
