"""
Hybrid Step Editor UI Component for GretahAI ScriptWeaver

This module provides the user interface for the hybrid AI-assisted test case editing system.
It allows users to view AI-generated steps (locked) and add manual steps at various insertion points.

Key Features:
- Visual distinction between AI and manual steps
- Step insertion interface with templates
- Real-time preview of combined test flow
- Flow validation and warnings
- Integration with StateManager
"""

import streamlit as st
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from core.step_templates import STEP_TEMPLATES, get_templates_by_category, get_template_categories, create_custom_step_template
from core.step_merger import StepMerger, merge_ai_and_manual_steps
from debug_utils import debug

logger = logging.getLogger("ScriptWeaver.hybrid_step_editor")


def render_hybrid_step_editor(state) -> bool:
    """
    Render the hybrid step editor interface.

    Args:
        state: StateManager instance

    Returns:
        bool: True if steps were modified, False otherwise
    """
    if not state.hybrid_editing_enabled:
        return False

    steps_modified = False

    st.markdown("### 🔀 Hybrid Step Editor")
    st.markdown("**AI-generated steps are locked** 🔒 to preserve optimization. You can add manual steps at any insertion point.")

    # Initialize hybrid editing data if needed
    if not state.ai_generated_steps and state.step_table_json:
        state.ai_generated_steps = state.step_table_json.copy()
        debug("Initialized AI-generated steps for hybrid editing")

    # Create tabs for different views
    tab1, tab2, tab3 = st.tabs(["📝 Edit Steps", "👁️ Preview", "✅ Validate"])

    with tab1:
        steps_modified = _render_step_editing_interface(state)

    with tab2:
        _render_step_preview(state)

    with tab3:
        _render_step_validation(state)

    return steps_modified


def _render_step_editing_interface(state) -> bool:
    """Render the step editing interface."""
    steps_modified = False

    if not state.ai_generated_steps:
        st.warning("No AI-generated steps available for hybrid editing.")
        return False

    # Step insertion controls
    st.markdown("#### ➕ Add Manual Steps")

    col1, col2 = st.columns([1, 1])

    with col1:
        # Insertion point selection
        merger = StepMerger()
        merger.set_ai_steps(state.ai_generated_steps)
        insertion_points = merger.get_insertion_points()

        selected_point = st.selectbox(
            "Insert at:",
            insertion_points,
            key="insertion_point_select",
            help="Choose where to insert the new step"
        )

    with col2:
        # Template category selection
        categories = get_template_categories()
        selected_category = st.selectbox(
            "Step Category:",
            ["Custom"] + categories,
            key="template_category_select",
            help="Choose a category of predefined steps"
        )

    # Template selection or custom step creation
    if selected_category == "Custom":
        steps_modified = _render_custom_step_creator(state, selected_point)
    else:
        steps_modified = _render_template_step_creator(state, selected_point, selected_category)

    # Display existing manual steps
    st.markdown("#### ✏️ Manual Steps")
    _render_existing_manual_steps(state)

    return steps_modified


def _render_custom_step_creator(state, insertion_point: str) -> bool:
    """Render interface for creating custom steps."""
    st.markdown("**Create Custom Step**")

    with st.form(key="custom_step_form"):
        col1, col2 = st.columns(2)

        with col1:
            action = st.text_input("Action", placeholder="e.g., click, type, verify")
            locator_strategy = st.selectbox("Locator Strategy", ["", "css", "xpath", "id", "name", "aria"])
            locator = st.text_input("Locator", placeholder="e.g., #button-id, //input[@name='username']")

        with col2:
            test_data_param = st.text_input("Test Data", placeholder="e.g., {{username}}, <EMAIL>")
            expected_result = st.text_input("Expected Result", placeholder="e.g., Button clicked successfully")
            timeout = st.number_input("Timeout (seconds)", min_value=1, max_value=300, value=10)

        submitted = st.form_submit_button("➕ Add Custom Step")

        if submitted and action:
            # Generate next step number
            next_step_no = _get_next_step_number(state, insertion_point)

            # Create custom step
            custom_step = create_custom_step_template(
                step_no=next_step_no,
                action=action,
                locator_strategy=locator_strategy,
                locator=locator,
                test_data_param=test_data_param,
                expected_result=expected_result,
                timeout=timeout
            )

            # Add to state
            _add_manual_step_to_state(state, custom_step, insertion_point)
            st.success(f"✅ Added custom step: {action}")
            st.rerun()
            return True

    return False


def _render_template_step_creator(state, insertion_point: str, category: str) -> bool:
    """Render interface for creating steps from templates."""
    templates = get_templates_by_category(category)

    if not templates:
        st.info(f"No templates available for category: {category}")
        return False

    st.markdown(f"**{category.title()} Templates**")

    # Template selection
    template_options = {f"{template.name} - {template.description}": template_id
                       for template_id, template in templates.items()}

    selected_template_display = st.selectbox(
        "Choose Template:",
        list(template_options.keys()),
        key=f"template_select_{category}"
    )

    if selected_template_display:
        template_id = template_options[selected_template_display]
        template = templates[template_id]

        # Show template details
        with st.expander("📋 Template Details", expanded=False):
            st.markdown(f"**Description:** {template.description}")
            st.markdown(f"**Category:** {template.category}")
            if template.customizable_fields:
                st.markdown(f"**Customizable Fields:** {', '.join(template.customizable_fields)}")

        # Customization form
        with st.form(key=f"template_form_{template_id}"):
            customizations = {}

            if template.customizable_fields:
                st.markdown("**Customize Template:**")
                col1, col2 = st.columns(2)

                for i, field in enumerate(template.customizable_fields):
                    current_value = template.step_data.get(field, "")

                    with col1 if i % 2 == 0 else col2:
                        if field == "timeout":
                            customizations[field] = st.number_input(
                                f"{field.replace('_', ' ').title()}:",
                                min_value=1, max_value=300,
                                value=int(current_value) if current_value else 10,
                                key=f"custom_{field}_{template_id}"
                            )
                        else:
                            customizations[field] = st.text_input(
                                f"{field.replace('_', ' ').title()}:",
                                value=str(current_value),
                                key=f"custom_{field}_{template_id}"
                            )

            submitted = st.form_submit_button(f"➕ Add {template.name}")

            if submitted:
                # Generate next step number
                next_step_no = _get_next_step_number(state, insertion_point)

                # Create step from template
                step = template.create_step(next_step_no, customizations)

                # Add to state
                _add_manual_step_to_state(state, step, insertion_point)
                st.success(f"✅ Added step: {template.name}")
                st.rerun()
                return True

    return False


def _render_existing_manual_steps(state):
    """Render existing manual steps with edit/delete options."""
    if not state.step_insertion_points:
        st.info("No manual steps added yet.")
        return

    for insertion_point, steps in state.step_insertion_points.items():
        if steps:
            with st.expander(f"📍 {insertion_point.replace('_', ' ').title()} ({len(steps)} steps)", expanded=False):
                for i, step in enumerate(steps):
                    col1, col2, col3 = st.columns([3, 1, 1])

                    with col1:
                        st.markdown(f"**Step {step.get('step_no', 'N/A')}:** {step.get('action', 'Unknown')}")
                        st.markdown(f"*Expected:* {step.get('expected_result', 'N/A')}")

                    with col2:
                        if st.button("✏️", key=f"edit_manual_{insertion_point}_{i}", help="Edit step"):
                            st.session_state[f"editing_step_{insertion_point}_{i}"] = True
                            st.rerun()

                    with col3:
                        if st.button("🗑️", key=f"delete_manual_{insertion_point}_{i}", help="Delete step"):
                            state.step_insertion_points[insertion_point].pop(i)
                            st.success("Step deleted")
                            st.rerun()

                    # Edit form (if editing)
                    if st.session_state.get(f"editing_step_{insertion_point}_{i}", False):
                        _render_step_edit_form(state, step, insertion_point, i)


def _render_step_edit_form(state, step: Dict[str, Any], insertion_point: str, step_index: int):
    """Render form for editing an existing manual step."""
    with st.form(key=f"edit_form_{insertion_point}_{step_index}"):
        st.markdown("**Edit Step**")

        col1, col2 = st.columns(2)

        with col1:
            action = st.text_input("Action", value=step.get("action", ""))
            locator_strategy = st.selectbox("Locator Strategy",
                                          ["", "css", "xpath", "id", "name", "aria"],
                                          index=["", "css", "xpath", "id", "name", "aria"].index(step.get("locator_strategy", "")))
            locator = st.text_input("Locator", value=step.get("locator", ""))

        with col2:
            test_data_param = st.text_input("Test Data", value=step.get("test_data_param", ""))
            expected_result = st.text_input("Expected Result", value=step.get("expected_result", ""))
            timeout = st.number_input("Timeout (seconds)", min_value=1, max_value=300,
                                    value=int(step.get("timeout", 10)))

        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("💾 Save Changes"):
                # Update step
                step.update({
                    "action": action,
                    "locator_strategy": locator_strategy,
                    "locator": locator,
                    "test_data_param": test_data_param,
                    "expected_result": expected_result,
                    "timeout": timeout,
                    "_modified_at": datetime.now().isoformat()
                })

                # Clear editing state
                del st.session_state[f"editing_step_{insertion_point}_{step_index}"]
                st.success("Step updated successfully")
                st.rerun()

        with col2:
            if st.form_submit_button("❌ Cancel"):
                del st.session_state[f"editing_step_{insertion_point}_{step_index}"]
                st.rerun()


def _render_step_preview(state):
    """Render preview of combined AI and manual steps."""
    st.markdown("#### 👁️ Combined Test Flow Preview")

    if not state.ai_generated_steps:
        st.warning("No AI-generated steps available.")
        return

    # Merge steps for preview
    merged_steps, markdown_table, _ = merge_ai_and_manual_steps(
        state.ai_generated_steps,
        state.step_insertion_points
    )

    if not merged_steps:
        st.info("No steps to preview.")
        return

    # Update combined step table in state
    state.combined_step_table = merged_steps

    # Display step count
    ai_count = len(state.ai_generated_steps)
    manual_count = sum(len(steps) for steps in state.step_insertion_points.values())
    total_count = len(merged_steps)

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("🤖 AI Steps", ai_count)
    with col2:
        st.metric("✏️ Manual Steps", manual_count)
    with col3:
        st.metric("📊 Total Steps", total_count)

    # Display combined table
    if markdown_table:
        st.markdown("**Combined Step Table:**")
        st.markdown(markdown_table)

    # Export options
    st.markdown("#### 📤 Export Options")
    col1, col2 = st.columns(2)

    with col1:
        if st.button("📋 Copy Combined Steps", key="copy_combined_steps"):
            st.session_state['clipboard_content'] = markdown_table
            st.success("✅ Combined steps copied to clipboard!")

    with col2:
        if st.button("💾 Apply to Test Case", key="apply_combined_steps"):
            # Update the main step table with combined steps
            state.step_table_json = merged_steps
            state.step_table_markdown = markdown_table
            state.combined_step_table = merged_steps

            # Update total_steps to reflect the new combined step count
            state.total_steps = len(merged_steps)

            # Reset step progress to start from the beginning with new step count
            state.current_step_index = 0
            state.all_steps_done = False

            # Clear any step-specific state that might be invalid with new steps
            state.selected_step_table_entry = None
            state.step_elements = []
            state.step_matches = {}
            state.element_matches = {}

            debug(f"Applied combined steps to test case: {len(merged_steps)} total steps")
            st.success("✅ Combined steps applied to test case!")
            st.rerun()


def _render_step_validation(state):
    """Render step flow validation results."""
    st.markdown("#### ✅ Step Flow Validation")

    if not state.ai_generated_steps:
        st.warning("No AI-generated steps available for validation.")
        return

    # Merge steps for validation
    merged_steps, _, _ = merge_ai_and_manual_steps(
        state.ai_generated_steps,
        state.step_insertion_points
    )

    if not merged_steps:
        st.info("No steps to validate.")
        return

    # Run comprehensive validation
    try:
        from helpers.step_validation import validate_combined_steps, get_validation_summary

        validation_result = validate_combined_steps(merged_steps)
        validation_dict = validation_result.to_dict()

        # Display validation summary
        summary = get_validation_summary(validation_result)
        if validation_dict["is_valid"]:
            st.success(f"✅ {summary}")
        else:
            st.error(f"❌ {summary}")

        # Create tabs for different types of issues
        if validation_dict["total_issues"] > 0 or validation_dict["total_suggestions"] > 0:
            error_tab, warning_tab, perf_tab, suggest_tab = st.tabs([
                f"🚨 Errors ({len(validation_dict['errors'])})",
                f"⚠️ Warnings ({len(validation_dict['warnings'])})",
                f"⚡ Performance ({len(validation_dict['performance_issues'])})",
                f"💡 Suggestions ({len(validation_dict['suggestions']) + len(validation_dict['best_practices'])})"
            ])

            # Display errors
            with error_tab:
                if validation_dict["errors"]:
                    for error in validation_dict["errors"]:
                        step_info = f" (Step {error['step_no']})" if error.get('step_no') else ""
                        st.error(f"• {error['message']}{step_info}")
                else:
                    st.success("✅ No errors found!")

            # Display warnings
            with warning_tab:
                if validation_dict["warnings"]:
                    for warning in validation_dict["warnings"]:
                        step_info = f" (Step {warning['step_no']})" if warning.get('step_no') else ""
                        st.warning(f"• {warning['message']}{step_info}")
                else:
                    st.success("✅ No warnings!")

            # Display performance issues
            with perf_tab:
                if validation_dict["performance_issues"]:
                    for issue in validation_dict["performance_issues"]:
                        step_info = f" (Step {issue['step_no']})" if issue.get('step_no') else ""
                        impact_emoji = "🔴" if issue.get('impact') == "high" else "🟡" if issue.get('impact') == "medium" else "🟢"
                        st.warning(f"{impact_emoji} {issue['message']}{step_info}")
                else:
                    st.success("✅ No performance issues!")

            # Display suggestions and best practices
            with suggest_tab:
                if validation_dict["suggestions"]:
                    st.markdown("**💡 Improvement Suggestions:**")
                    for suggestion in validation_dict["suggestions"]:
                        step_info = f" (Step {suggestion['step_no']})" if suggestion.get('step_no') else ""
                        st.info(f"• {suggestion['message']}{step_info}")

                if validation_dict["best_practices"]:
                    st.markdown("**📋 Best Practice Recommendations:**")
                    for practice in validation_dict["best_practices"]:
                        step_info = f" (Step {practice['step_no']})" if practice.get('step_no') else ""
                        st.info(f"• {practice['message']}{step_info}")

                if not validation_dict["suggestions"] and not validation_dict["best_practices"]:
                    st.success("✅ No suggestions - your test follows best practices!")

        else:
            st.success("🎉 Perfect! No issues found. Your test flow looks excellent.")

    except ImportError as e:
        st.error(f"❌ Failed to load validation module: {e}")
        # Fallback to basic validation
        _render_basic_validation(merged_steps)


def _render_basic_validation(merged_steps: List[Dict[str, Any]]):
    """Render basic validation as fallback when advanced validation is not available."""
    st.markdown("**Basic Validation (Fallback Mode)**")

    # Basic checks
    has_navigation = any(
        "navigate" in step.get("action", "").lower()
        for step in merged_steps
    )

    has_assertions = any(
        step.get("step_type") == "assertion" or step.get("assertion_type") not in ["", "no_error"]
        for step in merged_steps
    )

    has_cleanup = any(
        step.get("step_type") == "teardown"
        for step in merged_steps
    )

    # Display basic results
    col1, col2, col3 = st.columns(3)

    with col1:
        if has_navigation:
            st.success("✅ Navigation steps found")
        else:
            st.warning("⚠️ No navigation steps")

    with col2:
        if has_assertions:
            st.success("✅ Assertion steps found")
        else:
            st.warning("⚠️ No assertion steps")

    with col3:
        if has_cleanup:
            st.success("✅ Cleanup steps found")
        else:
            st.info("💡 Consider adding cleanup steps")

    st.info(f"📊 Total steps: {len(merged_steps)}")


def _add_manual_step_to_state(state, step: Dict[str, Any], insertion_point: str):
    """Add a manual step to the state manager."""
    if insertion_point not in state.step_insertion_points:
        state.step_insertion_points[insertion_point] = []

    state.step_insertion_points[insertion_point].append(step)
    debug(f"Added manual step to insertion point {insertion_point}: {step.get('action', 'Unknown')}")


def _get_next_step_number(state, insertion_point: str) -> str:
    """Generate the next step number for a manual step."""
    # Count existing steps at this insertion point
    existing_count = len(state.step_insertion_points.get(insertion_point, []))

    # Generate a temporary step number (will be renumbered during merge)
    return f"M{existing_count + 1}"


def enable_hybrid_editing(state) -> bool:
    """
    Enable hybrid editing mode for the current test case.

    Args:
        state: StateManager instance

    Returns:
        bool: True if hybrid editing was enabled successfully
    """
    if not state.step_table_json:
        return False

    state.hybrid_editing_enabled = True
    state.ai_generated_steps = state.step_table_json.copy()

    # Mark AI steps as locked
    for step in state.ai_generated_steps:
        step["_is_ai_generated"] = True
        step["_is_locked"] = True

    debug("Enabled hybrid editing mode")
    return True


def disable_hybrid_editing(state):
    """Disable hybrid editing mode and clear manual steps."""
    state.hybrid_editing_enabled = False
    state.step_insertion_points.clear()
    state.combined_step_table = None
    debug("Disabled hybrid editing mode")
