import json
import re
from pathlib import Path
import importlib.util
import random
import string
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.test_data_manager")

# Try to import token counter
try:
    from app_utils import token_counter
except ImportError:
    # Create a dummy token counter
    class DummyTokenCounter:
        def track_analysis(self, func):
            return func

        def track_generation(self, func):
            return func

    token_counter = DummyTokenCounter()

# Try to import core functionality
try:
    # Find the core.py file
    core_path = Path(__file__).parent / "core.py"
    if not core_path.exists():
        core_path = Path(__file__).parent.parent / "core.py"

    if not core_path.exists():
        raise ImportError(f"Could not find core.py in {Path(__file__).parent} or {Path(__file__).parent.parent}")

    # Import the module
    spec = importlib.util.spec_from_file_location("auto_test_generator_core", core_path)
    if spec is None:
        raise ImportError(f"Could not load spec from {core_path}")

    auto_test_generator_core = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(auto_test_generator_core)

    # Get functions from core
    generate_ai_response = getattr(auto_test_generator_core, 'generate_ai_response', None)
except ImportError as e:
    logger.error(f"Error importing core.py: {e}")
    generate_ai_response = None

# Try to import AI functionality
try:
    # Find the ai.py file
    ai_path = Path(__file__).parent / "core" / "ai.py"
    if not ai_path.exists():
        ai_path = Path(__file__).parent.parent / "core" / "ai.py"

    if not ai_path.exists():
        raise ImportError(f"Could not find ai.py in {Path(__file__).parent / 'core'} or {Path(__file__).parent.parent / 'core'}")

    # Import the module
    spec = importlib.util.spec_from_file_location("ai_module", ai_path)
    if spec is None:
        raise ImportError(f"Could not load spec from {ai_path}")

    ai_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(ai_module)

    # Get functions from ai module
    generate_test_data_prompt = getattr(ai_module, 'generate_test_data_prompt', None)
    generate_test_cases_data_prompt = getattr(ai_module, 'generate_test_cases_data_prompt', None)
except ImportError as e:
    logger.error(f"Error importing ai.py: {e}")
    generate_test_data_prompt = None
    generate_test_cases_data_prompt = None

def generate_random_email():
    """Generate a random email address"""
    username = ''.join(random.choices(string.ascii_lowercase, k=8))
    domain = ''.join(random.choices(string.ascii_lowercase, k=6))
    return f"{username}@{domain}.com"

def generate_random_password():
    """Generate a random password"""
    chars = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(random.choices(chars, k=10))

def generate_random_name():
    """Generate a random name"""
    first_names = ["John", "Jane", "Michael", "Emma", "David", "Sarah", "Robert", "Lisa"]
    last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Miller", "Davis", "Wilson"]
    return f"{random.choice(first_names)} {random.choice(last_names)}"

def generate_random_phone():
    """Generate a random phone number"""
    return ''.join(random.choices(string.digits, k=10))

def generate_test_data_dictionary(test_cases):
    """
    Generate a comprehensive test data dictionary based on test cases.

    Args:
        test_cases (list): List of test cases

    Returns:
        dict: Dictionary of test data
    """
    # Initialize test data with common defaults
    test_data = {
        # Login data
        "valid_email": "<EMAIL>",
        "invalid_email": "invalid.email",
        "valid_password": "Password123!",
        "invalid_password": "pass",

        # User data
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe",
        "phone_number": "1234567890",
        "mobile_number": "9876543210",

        # Payment data
        "card_number": "****************",
        "card_expiry": "12/25",
        "card_cvv": "123",
        "card_holder": "John Doe",

        # Random data
        "random_email": generate_random_email(),
        "random_password": generate_random_password(),
        "random_name": generate_random_name(),
        "random_phone": generate_random_phone()
    }

    # If we have AI capabilities, use them to enhance test data
    if generate_ai_response:
        try:
            # Use the prompt function from ai.py if available
            if generate_test_cases_data_prompt:
                prompt = generate_test_cases_data_prompt(test_cases)
                logger.info("Using prompt from ai.py for test data dictionary generation")
            else:
                # Fallback to inline prompt generation
                logger.warning("Falling back to inline prompt generation for test data dictionary")
                test_cases_summary = []
                for tc in test_cases[:5]:  # Limit to first 5 test cases to avoid token limits
                    if isinstance(tc, dict):
                        tc_id = tc.get('Test Case ID', '')
                        objective = tc.get('Test Case Objective', '')
                        steps = []
                        for step in tc.get('Steps', []):
                            if isinstance(step, dict):
                                steps.append({
                                    "action": step.get('Test Steps', ''),
                                    "expected": step.get('Expected Result', '')
                                })
                        test_cases_summary.append({
                            "id": tc_id,
                            "objective": objective,
                            "steps": steps[:3]  # Limit to first 3 steps
                        })

                prompt = f"""
                I need to generate test data for the following test cases:
                {json.dumps(test_cases_summary, indent=2)}

                Please enhance this existing test data with more appropriate values based on the test cases:
                {json.dumps(test_data, indent=2)}

                Return only a JSON object with the enhanced test data. Keep the same keys but improve the values.
                """

            # Get AI response
            ai_response = generate_ai_response(prompt)

            # Parse the response to get enhanced test data
            try:
                # Try to extract JSON from the response
                json_match = re.search(r'```json\n(.*?)\n```', ai_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                else:
                    json_str = ai_response

                # Clean up the JSON string
                json_str = re.sub(r'```.*?```', '', json_str, flags=re.DOTALL)

                # Parse the JSON
                parsed_data = json.loads(json_str)

                if isinstance(parsed_data, dict):
                    # Update test data with AI-enhanced values
                    test_data.update(parsed_data)
                    logger.info(f"Enhanced test data with AI-generated values")
            except Exception as e:
                logger.error(f"Error parsing AI response for test data enhancement: {e}")
        except Exception as e:
            logger.error(f"Error using AI to enhance test data: {e}")

    return test_data

@token_counter.track_analysis
def generate_test_data_for_step(step_data, ui_elements, step_table_entry=None):
    """
    Generate test data for a specific test step.

    Args:
        step_data (dict): Test step data with action and expected result
        ui_elements (list): List of UI elements relevant to the test step
        step_table_entry (dict, optional): Step table entry with structured data

    Returns:
        dict: Test data for the step
    """
    logger.info("=== ENTERING generate_test_data_for_step ===")
    logger.info(f"Input step_data: {step_data}")
    logger.info(f"Input ui_elements: {type(ui_elements)} with {len(ui_elements) if isinstance(ui_elements, list) else 'N/A'} elements")
    logger.info(f"Input step_table_entry: {step_table_entry}")

    # Default test data
    test_data = {}
    token_usage = 0

    logger.info("Initializing test_data as empty dictionary")

    # First, extract test data from step table if available
    if step_table_entry and isinstance(step_table_entry, dict):
        logger.info("Extracting test data from step table entry")
        step_table_test_data = extract_test_data_from_step_table(step_table_entry)
        logger.info(f"Extracted test data from step table: {step_table_test_data}")
        test_data.update(step_table_test_data)
        logger.info(f"Updated test_data with step table values: {test_data}")

        # If we have step_type and action in the step table, use them in the prompt
        step_type = step_table_entry.get('step_type', '')
        action = step_table_entry.get('action', '')
        if step_type and action:
            logger.info(f"Using step_type '{step_type}' and action '{action}' from step table")
            step_data['step_type'] = step_type
            step_data['action'] = action

    # If we have AI capabilities, use them to generate test data
    if generate_ai_response:
        logger.info("AI response generation is available, attempting to use it")
        try:
            # Use the prompt function from ai.py if available
            if generate_test_data_prompt:
                logger.info("Using generate_test_data_prompt from ai.py")
                prompt = generate_test_data_prompt(step_data, ui_elements, step_table_entry)
                logger.info("Successfully generated prompt from ai.py")
            else:
                # Fallback to inline prompt generation
                logger.warning("Falling back to inline prompt generation for test data")
                step_table_info = ""
                if step_table_entry and isinstance(step_table_entry, dict):
                    logger.info("Building step table info for prompt")
                    step_table_info = f"""
                    Step Table Information:
                    - Step Type: {step_table_entry.get('step_type', 'N/A')}
                    - Action: {step_table_entry.get('action', 'N/A')}
                    - Locator Strategy: {step_table_entry.get('locator_strategy', 'N/A')}
                    - Locator: {step_table_entry.get('locator', 'N/A')}
                    - Test Data Parameter: {step_table_entry.get('test_data_param', 'N/A')}
                    - Expected Result: {step_table_entry.get('expected_result', 'N/A')}
                    - Assertion Type: {step_table_entry.get('assertion_type', 'N/A')}
                    """

                logger.info("Building inline prompt")
                prompt = f"""
                I need to generate realistic and contextually appropriate test data for the following test step:
                - Action: {step_data.get('action', '')}
                - Expected Result: {step_data.get('expected', '')}

                {step_table_info}

                The UI elements involved in this step are:
                {json.dumps(ui_elements, indent=2)}

                Please generate high-quality test data for this step as a JSON object with key-value pairs.
                Each key should be a variable name for the test data, and each value should be the test data value.

                IMPORTANT GUIDELINES:
                1. Generate REALISTIC data that would be used in a real-world scenario, not generic placeholders
                2. If the step involves form submission, include ALL required fields for that form
                3. If the step involves validation testing, include both valid and invalid test data
                4. For text fields, use realistic content (not "test_fieldname")
                5. For emails, use realistic formats like "<EMAIL>"
                6. For names, use realistic full names like "John Smith" or "Maria Rodriguez"
                7. For addresses, use realistic street addresses, cities, states, and zip codes
                8. For credit cards, use valid test card numbers (e.g., "****************" for Visa)
                9. For dates, use proper date formats appropriate to the context
                10. If the step table has test data parameters in {{{{param}}}} format, prioritize generating values for these parameters

                Example of high-quality test data:
                {{
                  "email": "<EMAIL>",
                  "password": "SecurePass123!",
                  "first_name": "John",
                  "last_name": "Smith",
                  "address": "123 Main Street",
                  "city": "Boston",
                  "state": "MA",
                  "zip_code": "02108",
                  "credit_card": "****************",
                  "expiry_date": "12/25",
                  "cvv": "123"
                }}

                Return ONLY the JSON object with no additional text or explanation.
                """
                logger.info("Successfully built inline prompt")

            # Get AI response
            logger.info("Calling generate_ai_response")
            ai_response = generate_ai_response(prompt)
            logger.info(f"AI response type: {type(ai_response)}")

            # Log the first 100 characters of the response for debugging
            if isinstance(ai_response, str):
                logger.info(f"AI response (first 100 chars): {ai_response[:100]}")
            elif isinstance(ai_response, dict):
                logger.info(f"AI response keys: {list(ai_response.keys())}")
            else:
                logger.info(f"AI response is neither string nor dict: {type(ai_response)}")

            # Track token usage if available
            logger.info("Checking for token_usage in AI response")
            if isinstance(ai_response, dict) and 'token_usage' in ai_response:
                logger.info(f"Found token_usage in AI response: {ai_response['token_usage']}")
                token_usage = ai_response['token_usage']
                ai_response = ai_response.get('response', '')
                logger.info("Extracted response from AI response dictionary")
            else:
                logger.info("No token_usage found in AI response, assuming response is the raw text")

            # Parse the response to get test data
            logger.info("Attempting to parse AI response to extract test data")
            try:
                # Try to extract JSON from the response
                logger.info("Looking for JSON code block in response")
                json_match = re.search(r'```json\n(.*?)\n```', ai_response, re.DOTALL)
                if json_match:
                    logger.info("Found JSON code block")
                    json_str = json_match.group(1)
                else:
                    logger.info("No JSON code block found, using entire response")
                    json_str = ai_response

                # Clean up the JSON string
                logger.info("Cleaning up JSON string")
                json_str = re.sub(r'```.*?```', '', json_str, flags=re.DOTALL)
                json_str = re.sub(r'[^\{\}:,\"\'\\[\\]\d\.\-\w\s]', '', json_str)
                logger.info(f"Cleaned JSON string (first 100 chars): {json_str[:100]}")

                # Parse the JSON
                logger.info("Parsing JSON string")
                parsed_data = json.loads(json_str)
                logger.info(f"Successfully parsed JSON, result type: {type(parsed_data)}")

                if isinstance(parsed_data, dict):
                    logger.info(f"Parsed data is a dictionary with {len(parsed_data)} keys")
                    # Update test data with AI-generated values
                    # but don't overwrite step table values
                    for key, value in parsed_data.items():
                        if key not in test_data:
                            test_data[key] = value
                            logger.info(f"Added test data: {key} = {value}")
                        else:
                            logger.info(f"Skipped existing test data: {key} = {test_data[key]}")
                    logger.info(f"Generated {len(parsed_data)} test data items for step")
                else:
                    logger.warning(f"Parsed data is not a dictionary, it's a {type(parsed_data)}")
            except Exception as e:
                logger.error(f"Error parsing AI response for test data: {e}")
                logger.error(f"JSON parsing error details: {str(e)}")
                logger.error(f"Response that failed to parse: {ai_response[:200]}...")
                # Fall back to simple test data generation
        except Exception as e:
            logger.error(f"Error using AI to generate test data: {e}")
            logger.error(f"Exception details: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    # If AI generation failed or is not available, use enhanced test data generation
    if not test_data:
        logger.info("No test data generated via AI, falling back to enhanced test data generation")
        # Extract input fields from UI elements
        for element in ui_elements:
            attrs = element.get('attributes', {})
            element_type = attrs.get('type', '').lower()
            element_tag = attrs.get('tag', '').lower()
            element_name = element.get('name', '').lower()
            element_id = attrs.get('id', '').lower()
            element_placeholder = attrs.get('placeholder', '').lower()

            # Use the most specific identifier available
            field_name = element_name or element_id or element_placeholder or f"field_{random.randint(1000, 9999)}"

            # Generate test data based on element type and context
            if element_tag == 'input':
                if element_type == 'text':
                    # Try to infer the field purpose from its name/id/placeholder
                    if any(word in field_name for word in ['name', 'user']):
                        if 'first' in field_name:
                            test_data[field_name] = generate_random_name().split()[0]
                        elif 'last' in field_name:
                            test_data[field_name] = generate_random_name().split()[1]
                        else:
                            test_data[field_name] = generate_random_name()
                    elif any(word in field_name for word in ['email']):
                        test_data[field_name] = generate_random_email()
                    elif any(word in field_name for word in ['phone', 'mobile', 'tel']):
                        test_data[field_name] = generate_random_phone()
                    elif any(word in field_name for word in ['address', 'street']):
                        streets = ["123 Main St", "456 Oak Ave", "789 Pine Blvd", "101 Maple Dr"]
                        test_data[field_name] = random.choice(streets)
                    elif any(word in field_name for word in ['city']):
                        cities = ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix"]
                        test_data[field_name] = random.choice(cities)
                    elif any(word in field_name for word in ['state']):
                        states = ["CA", "NY", "TX", "FL", "IL"]
                        test_data[field_name] = random.choice(states)
                    elif any(word in field_name for word in ['zip', 'postal']):
                        test_data[field_name] = str(random.randint(10000, 99999))
                    elif any(word in field_name for word in ['search', 'query']):
                        test_data[field_name] = "product search query"
                    else:
                        # More meaningful default than "test_fieldname"
                        test_data[field_name] = f"Sample text for {field_name.replace('_', ' ')}"
                elif element_type == 'email':
                    test_data[field_name] = generate_random_email()
                elif element_type == 'password':
                    test_data[field_name] = generate_random_password() + "!A9"
                elif element_type == 'number':
                    test_data[field_name] = str(random.randint(1, 1000))
                elif element_type == 'checkbox':
                    test_data[field_name] = random.choice(["true", "false"])
                elif element_type == 'radio':
                    test_data[field_name] = f"option{random.randint(1, 3)}"
                elif element_type == 'date':
                    # Generate a date in YYYY-MM-DD format
                    year = datetime.now().year
                    month = random.randint(1, 12)
                    day = random.randint(1, 28)
                    test_data[field_name] = f"{year}-{month:02d}-{day:02d}"
            elif element_tag == 'textarea':
                messages = [
                    "This is a sample message for testing purposes.",
                    "Here is some example text for the textarea field.",
                    "Testing with a multi-line comment.\nThis is the second line.",
                    "Automation test data for this textarea field."
                ]
                test_data[field_name] = random.choice(messages)
            elif element_tag == 'select':
                test_data[field_name] = f"option{random.randint(1, 5)}"

    # Add common test data for specific contexts if not already present
    action_lower = step_data.get('action', '').lower()
    if any(word in action_lower for word in ['login', 'sign in', 'username', 'password']):
        # Only add these if they don't already exist
        for key, value in {
            'username': generate_random_name().split()[0].lower() + str(random.randint(100, 999)),
            'email': generate_random_email(),
            'password': "Password123!"
        }.items():
            if key not in test_data:
                test_data[key] = value
    elif any(word in action_lower for word in ['register', 'sign up', 'create account']):
        # Only add these if they don't already exist
        for key, value in {
            'username': generate_random_name().split()[0].lower() + str(random.randint(100, 999)),
            'email': generate_random_email(),
            'password': generate_random_password() + "!A9",
            'confirm_password': generate_random_password() + "!A9",
            'first_name': generate_random_name().split()[0],
            'last_name': generate_random_name().split()[1]
        }.items():
            if key not in test_data:
                test_data[key] = value

                # Make confirm_password match password
                if key == 'password':
                    test_data['confirm_password'] = value
    elif any(word in action_lower for word in ['search', 'find', 'query']):
        if 'search_query' not in test_data:
            test_data['search_query'] = "sample search query"
    elif any(word in action_lower for word in ['payment', 'checkout', 'credit card']):
        # Only add these if they don't already exist
        for key, value in {
            'card_number': "****************",
            'card_expiry': f"{random.randint(1, 12):02d}/{(datetime.now().year % 100) + random.randint(1, 5):02d}",
            'card_cvv': str(random.randint(100, 999)),
            'card_holder': generate_random_name()
        }.items():
            if key not in test_data:
                test_data[key] = value

    # Return the test data dictionary
    # Note: We no longer track token_usage in this function
    # since all API calls are now routed through generate_llm_response
    return test_data

def format_test_data_as_python(test_data):
    """
    Format test data as Python code.

    Args:
        test_data (dict): Test data dictionary

    Returns:
        str: Python code representing the test data
    """
    lines = ["# Test Data"]
    lines.append("test_data = {")

    for key, value in test_data.items():
        if isinstance(value, str):
            lines.append(f"    '{key}': '{value}',")
        elif isinstance(value, bool):
            lines.append(f"    '{key}': {str(value).lower()},")
        else:
            lines.append(f"    '{key}': {value},")

    lines.append("}")

    return "\n".join(lines)
def extract_test_data_from_step_table(step_table_entry):
    """
    Extract test data parameters from a step table entry.

    Args:
        step_table_entry (dict): A step table entry with test_data_param field

    Returns:
        dict: Extracted test data parameters
    """
    test_data = {}

    # Check if we have a test_data_param field
    test_data_param = step_table_entry.get('test_data_param', '')

    if not test_data_param:
        return test_data

    # Extract parameters in {{param}} format
    param_pattern = r'\{\{([^}]+)\}\}'
    matches = re.findall(param_pattern, test_data_param)

    # Get step context for better test data generation
    step_type = step_table_entry.get('step_type', '').lower()
    action = step_table_entry.get('action', '').lower()
    expected_result = step_table_entry.get('expected_result', '').lower()

    # Generate realistic values for each parameter
    for param in matches:
        param_name = param.strip()
        param_lower = param_name.lower()

        # Email addresses with different formats based on context
        if 'email' in param_lower:
            if 'invalid' in param_lower or ('invalid' in expected_result and 'email' in expected_result):
                if 'special_chars' in param_lower:
                    test_data[param_name] = "user!@#$%^&*()@example.com"
                elif 'missing_at' in param_lower:
                    test_data[param_name] = "userexample.com"
                elif 'missing_domain' in param_lower:
                    test_data[param_name] = "user@"
                elif 'double_at' in param_lower:
                    test_data[param_name] = "user@@example.com"
                elif 'no_tld' in param_lower:
                    test_data[param_name] = "user@example"
                else:
                    test_data[param_name] = "invalid.email@"
            else:
                # Generate a more realistic email
                if 'business' in param_lower:
                    test_data[param_name] = f"{generate_random_name().lower().replace(' ', '.')}@company.com"
                elif 'personal' in param_lower:
                    test_data[param_name] = f"{generate_random_name().lower().replace(' ', '')}@gmail.com"
                else:
                    test_data[param_name] = generate_random_email()

        # Passwords with different security levels
        elif 'password' in param_lower:
            if 'invalid' in param_lower or 'weak' in param_lower:
                if 'short' in param_lower:
                    test_data[param_name] = "pass"
                elif 'no_special' in param_lower:
                    test_data[param_name] = "Password123"
                elif 'no_number' in param_lower:
                    test_data[param_name] = "Password!"
                elif 'no_uppercase' in param_lower:
                    test_data[param_name] = "password123!"
                else:
                    test_data[param_name] = "weak"
            elif 'strong' in param_lower:
                test_data[param_name] = f"{generate_random_password()}!A9"
            else:
                test_data[param_name] = "Password123!"

            # If there's a confirm_password parameter, make sure it matches or doesn't match based on context
            if 'confirm' in param_lower:
                if 'mismatch' in param_lower or 'not_match' in param_lower:
                    test_data[param_name] = "Password123!!"  # Slightly different
                elif param_lower.replace('confirm_', '') in test_data:
                    # Make it match the original password
                    original_param = param_lower.replace('confirm_', '')
                    for key in test_data:
                        if key.lower() == original_param:
                            test_data[param_name] = test_data[key]
                            break

        # Usernames with different formats
        elif 'username' in param_lower or ('user' in param_lower and 'name' in param_lower):
            if 'invalid' in param_lower:
                if 'special_chars' in param_lower:
                    test_data[param_name] = "user!@#$%^&*()"
                elif 'too_short' in param_lower:
                    test_data[param_name] = "usr"
                elif 'too_long' in param_lower:
                    test_data[param_name] = "a" * 50
                else:
                    test_data[param_name] = "invalid@user"
            else:
                # Generate a username based on a random name
                random_name = generate_random_name()
                first_name = random_name.split()[0]
                test_data[param_name] = f"{first_name.lower()}{random.randint(100, 999)}"

        # Names with different formats
        elif 'name' in param_lower:
            if 'first' in param_lower or 'fname' in param_lower:
                test_data[param_name] = generate_random_name().split()[0]
            elif 'last' in param_lower or 'lname' in param_lower:
                test_data[param_name] = generate_random_name().split()[1]
            elif 'full' in param_lower:
                test_data[param_name] = generate_random_name()
            else:
                test_data[param_name] = generate_random_name()

        # Phone numbers with different formats
        elif 'phone' in param_lower or 'mobile' in param_lower:
            if 'invalid' in param_lower:
                if 'letters' in param_lower:
                    test_data[param_name] = "123-abc-4567"
                elif 'too_short' in param_lower:
                    test_data[param_name] = "123456"
                elif 'special_chars' in param_lower:
                    test_data[param_name] = "************!"
                else:
                    test_data[param_name] = "not-a-number"
            else:
                phone = generate_random_phone()
                if 'formatted' in param_lower:
                    test_data[param_name] = f"({phone[:3]})-{phone[3:6]}-{phone[6:]}"
                else:
                    test_data[param_name] = phone

        # Search queries based on context
        elif 'search' in param_lower or 'query' in param_lower:
            if 'product' in param_lower or 'product' in action:
                products = ["smartphone", "laptop", "headphones", "smart watch", "tablet", "camera", "bluetooth speaker"]
                test_data[param_name] = random.choice(products)
            elif 'location' in param_lower or 'location' in action:
                locations = ["New York", "London", "Tokyo", "Paris", "Sydney", "Berlin", "Toronto"]
                test_data[param_name] = random.choice(locations)
            elif 'person' in param_lower or 'person' in action:
                test_data[param_name] = generate_random_name()
            else:
                test_data[param_name] = "test search query"

        # URLs with different formats
        elif 'url' in param_lower or 'link' in param_lower:
            if 'invalid' in param_lower:
                test_data[param_name] = "invalid-url"
            elif 'https' in param_lower:
                test_data[param_name] = "https://www.example.com"
            elif 'http' in param_lower:
                test_data[param_name] = "http://www.example.com"
            else:
                test_data[param_name] = "https://www.example.com"

        # Credit card information
        elif 'card' in param_lower:
            if 'number' in param_lower:
                if 'invalid' in param_lower:
                    test_data[param_name] = "1234-5678-9012-3456"
                elif 'visa' in param_lower:
                    test_data[param_name] = "****************"
                elif 'mastercard' in param_lower:
                    test_data[param_name] = "****************"
                elif 'amex' in param_lower:
                    test_data[param_name] = "***************"
                else:
                    test_data[param_name] = "****************"
            elif 'cvv' in param_lower or 'cvc' in param_lower:
                if 'invalid' in param_lower:
                    test_data[param_name] = "12"
                else:
                    test_data[param_name] = str(random.randint(100, 999))
            elif 'expiry' in param_lower or 'expiration' in param_lower:
                # Generate a future date
                current_year = datetime.now().year % 100  # Get last two digits
                current_month = datetime.now().month
                year = current_year + random.randint(1, 5)
                month = random.randint(1, 12)
                test_data[param_name] = f"{month:02d}/{year:02d}"
            elif 'holder' in param_lower or 'name' in param_lower:
                test_data[param_name] = generate_random_name()

        # Address information
        elif 'address' in param_lower:
            if 'line1' in param_lower or 'street' in param_lower:
                streets = ["123 Main St", "456 Oak Ave", "789 Pine Blvd", "101 Maple Dr", "202 Cedar Ln"]
                test_data[param_name] = random.choice(streets)
            elif 'line2' in param_lower or 'apt' in param_lower:
                apts = ["Apt 101", "Suite 202", "Unit 303", "Apt 404", "Suite 505"]
                test_data[param_name] = random.choice(apts)
            elif 'city' in param_lower:
                cities = ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia"]
                test_data[param_name] = random.choice(cities)
            elif 'state' in param_lower:
                states = ["CA", "NY", "TX", "FL", "IL", "PA", "OH", "GA", "NC", "MI"]
                test_data[param_name] = random.choice(states)
            elif 'zip' in param_lower or 'postal' in param_lower:
                test_data[param_name] = str(random.randint(10000, 99999))
            elif 'country' in param_lower:
                countries = ["United States", "Canada", "United Kingdom", "Australia", "Germany", "France"]
                test_data[param_name] = random.choice(countries)
            else:
                test_data[param_name] = "123 Main St, Anytown, CA 12345"

        # Date information
        elif 'date' in param_lower:
            from datetime import datetime, timedelta

            today = datetime.now()
            if 'birth' in param_lower or 'dob' in param_lower:
                # Generate a date 18-80 years ago
                years_ago = random.randint(18, 80)
                birth_date = today - timedelta(days=365 * years_ago)
                test_data[param_name] = birth_date.strftime("%m/%d/%Y")
            elif 'future' in param_lower:
                # Generate a date 1-30 days in the future
                days_ahead = random.randint(1, 30)
                future_date = today + timedelta(days=days_ahead)
                test_data[param_name] = future_date.strftime("%m/%d/%Y")
            elif 'past' in param_lower:
                # Generate a date 1-30 days in the past
                days_ago = random.randint(1, 30)
                past_date = today - timedelta(days=days_ago)
                test_data[param_name] = past_date.strftime("%m/%d/%Y")
            else:
                test_data[param_name] = today.strftime("%m/%d/%Y")

        # Special characters for testing
        elif 'special_chars' in param_lower:
            test_data[param_name] = "!@#$%^&*()_+-=[]{}|;':\",./<>?"

        # Numeric values
        elif 'number' in param_lower or 'numeric' in param_lower:
            if 'decimal' in param_lower or 'float' in param_lower:
                test_data[param_name] = str(round(random.uniform(1, 1000), 2))
            else:
                test_data[param_name] = str(random.randint(1, 1000))

        # Boolean values
        elif 'boolean' in param_lower or 'bool' in param_lower:
            test_data[param_name] = random.choice(["true", "false"])

        # Default case - generate a more meaningful value based on parameter name
        else:
            # Try to infer the type from the parameter name
            if any(word in param_lower for word in ['id', 'code', 'number']):
                test_data[param_name] = str(random.randint(10000, 99999))
            elif any(word in param_lower for word in ['amount', 'price', 'cost']):
                test_data[param_name] = str(round(random.uniform(10, 1000), 2))
            elif any(word in param_lower for word in ['comment', 'message', 'description']):
                messages = [
                    "This is a test message for automation purposes.",
                    "Sample comment for testing the application.",
                    "Testing the input field with this description.",
                    "Automation test data for this field."
                ]
                test_data[param_name] = random.choice(messages)
            else:
                # If we can't infer the type, use a more descriptive value than just "test_X"
                test_data[param_name] = f"Sample value for {param_name}"

    return test_data

# This is a duplicate function that was mistakenly included in the file.
# The actual implementation is above at line 175.
# This duplicate is removed to avoid confusion and potential bugs.