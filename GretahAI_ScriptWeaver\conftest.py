'''
Pytest configuration file for automated test framework.
This module provides fixtures, hooks, and utilities for UI test automation with Selenium.
It handles browser instantiation, test logging, screenshot capture, and reporting.
Constants:
    SCREENSHOT_DIR: Directory where test screenshots are saved
    LOG_DIR: Directory for all log files
    TEST_LOGS_DIR: Directory for test-specific logs
    PAGE_SOURCE_DIR: Directory for HTML page sources
Fixtures:
    setup_session: Session-level fixture for test session setup and teardown
    browser: Creates and manages a WebDriver instance with automatic cleanup
    take_timed_screenshots: Allows taking screenshots during test execution
    log_message: Enables adding custom log messages from tests
    performance_monitor: Tracks and reports test execution time
Hooks:
    pytest_runtest_makereport: Captures test artifacts (screenshots, page source)
    pytest_terminal_summary: Generates test execution summary report
Utility Functions:
    setup_logging: Configures global logging
    add_test_log_handler: Creates test-specific log handlers
    take_screenshot: Captures and saves browser screenshots
The framework automatically:
- Creates unique log files for each test
- Takes screenshots on test failures
- Saves page source for debugging
- Generates execution summary reports
- Tracks browser console logs
'''

import pytest
import os
import time
import json
import logging
import traceback
import psutil
from pathlib import Path
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import xml.etree.ElementTree as ET

# ================================
# Directory Configuration
# ================================
# Base directory for all test artifacts
BASE_DIR = Path(os.getcwd())
SCREENSHOT_DIR = BASE_DIR / "screenshots"
LOG_DIR = BASE_DIR / "logs"
TEST_LOGS_DIR = LOG_DIR / "test_logs"
PAGE_SOURCE_DIR = BASE_DIR / "page_sources"

# Create directories if they don't exist
for directory in [SCREENSHOT_DIR, LOG_DIR, TEST_LOGS_DIR, PAGE_SOURCE_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# ================================
# Logging Configuration
# ================================
def setup_logging():
    """Configure global logging for the test framework."""
    # Create a logger for the test framework
    logger = logging.getLogger("test_framework")
    logger.setLevel(logging.DEBUG)

    # Prevent duplicate handlers
    if logger.handlers:
        return logger

    # Check if quiet mode is enabled (default: True for cleaner console output)
    quiet_mode = os.environ.get('PYTEST_QUIET_MODE', '1') == '1'

    # Create console handler with appropriate level
    console_handler = logging.StreamHandler()
    if quiet_mode:
        # In quiet mode, only show warnings and errors in console
        console_handler.setLevel(logging.WARNING)
    else:
        # In verbose mode, show info and above
        console_handler.setLevel(logging.INFO)

    # Create file handler for framework logs (always detailed)
    framework_log_file = LOG_DIR / "test_framework.log"
    file_handler = logging.FileHandler(framework_log_file)
    file_handler.setLevel(logging.DEBUG)

    # Create formatter
    if quiet_mode:
        # Simpler format for console in quiet mode
        console_formatter = logging.Formatter('%(levelname)s: %(message)s')
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    else:
        # Full format for both in verbose mode
        console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    console_handler.setFormatter(console_formatter)
    file_handler.setFormatter(file_formatter)

    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger

# Initialize global logger
logger = setup_logging()

def add_test_log_handler(test_node):
    """
    Create a test-specific log handler and return the handler and log file path.

    Args:
        test_node: The pytest test node

    Returns:
        tuple: (log_handler, log_file_path)
    """
    # Create test-specific log file name
    # Handle both Function and Module nodes
    if hasattr(test_node, 'function') and test_node.function:
        test_name = test_node.function.__name__
        class_name = test_node.cls.__name__ if test_node.cls else "nogroup"
    else:
        # Fallback for Module nodes or other node types
        test_name = getattr(test_node, 'name', 'unknown_test')
        class_name = "nogroup"

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if class_name == "nogroup":
        log_filename = f"{test_name}_{timestamp}.log"
    else:
        log_filename = f"{class_name}_{test_name}_{timestamp}.log"

    log_file_path = TEST_LOGS_DIR / log_filename

    # Create file handler for this specific test
    test_handler = logging.FileHandler(log_file_path)
    test_handler.setLevel(logging.DEBUG)

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    test_handler.setFormatter(formatter)

    # Add handler to the test framework logger
    logger.addHandler(test_handler)

    return test_handler, str(log_file_path)

# ================================
# Performance Monitoring Utilities
# ================================
def get_memory_usage():
    """Get current memory usage in MB."""
    process = psutil.Process()
    return process.memory_info().rss / 1024 / 1024

def parse_network_metrics_from_logs(performance_logs):
    """
    Parse network metrics from Chrome performance logs.

    Args:
        performance_logs: List of performance log entries

    Returns:
        dict: Network metrics including request count and bytes transferred
    """
    network_requests = 0
    network_bytes = 0

    try:
        for log_entry in performance_logs:
            if isinstance(log_entry, dict) and 'message' in log_entry:
                message = log_entry['message']
                if isinstance(message, dict):
                    method = message.get('method', '')
                    if method in ['Network.responseReceived', 'Network.dataReceived']:
                        network_requests += 1
                        # Try to extract response length if available
                        params = message.get('params', {})
                        if 'response' in params:
                            response = params['response']
                            if 'encodedDataLength' in response:
                                network_bytes += response['encodedDataLength']
    except Exception as e:
        logger.error(f"Error parsing network metrics: {e}")

    return {
        "network_requests": network_requests,
        "network_bytes": network_bytes
    }

def extract_browser_timing_metrics(driver):
    """
    Extract browser timing metrics using JavaScript.

    Args:
        driver: WebDriver instance

    Returns:
        dict: Browser timing metrics
    """
    try:
        # Execute JavaScript to get navigation timing
        timing_script = """
        var timing = window.performance.timing;
        var navigation = window.performance.navigation;
        return {
            'navigation_type': navigation.type,
            'redirect_count': navigation.redirectCount,
            'navigation_start': timing.navigationStart,
            'unload_start': timing.unloadEventStart,
            'unload_end': timing.unloadEventEnd,
            'redirect_start': timing.redirectStart,
            'redirect_end': timing.redirectEnd,
            'fetch_start': timing.fetchStart,
            'domain_lookup_start': timing.domainLookupStart,
            'domain_lookup_end': timing.domainLookupEnd,
            'connect_start': timing.connectStart,
            'connect_end': timing.connectEnd,
            'secure_connection_start': timing.secureConnectionStart,
            'request_start': timing.requestStart,
            'response_start': timing.responseStart,
            'response_end': timing.responseEnd,
            'dom_loading': timing.domLoading,
            'dom_interactive': timing.domInteractive,
            'dom_content_loaded_start': timing.domContentLoadedEventStart,
            'dom_content_loaded_end': timing.domContentLoadedEventEnd,
            'dom_complete': timing.domComplete,
            'load_start': timing.loadEventStart,
            'load_end': timing.loadEventEnd
        };
        """

        timing_data = driver.execute_script(timing_script)

        # Calculate derived metrics
        if timing_data and timing_data.get('navigation_start'):
            nav_start = timing_data['navigation_start']
            metrics = {}

            if timing_data.get('load_end') and timing_data.get('navigation_start'):
                metrics['total_page_load'] = timing_data['load_end'] - nav_start

            if timing_data.get('response_end') and timing_data.get('request_start'):
                metrics['server_response_time'] = timing_data['response_end'] - timing_data['request_start']

            if timing_data.get('dom_complete') and timing_data.get('dom_loading'):
                metrics['dom_processing_time'] = timing_data['dom_complete'] - timing_data['dom_loading']

            return metrics

    except Exception as e:
        logger.error(f"Error extracting browser timing metrics: {e}")

    return {}

# ================================
# Session-level Fixtures
# ================================
@pytest.fixture(scope="session", autouse=True)
def setup_session():
    """Session-level fixture for test session setup and teardown."""
    logger.info("=== Test Session Starting ===")
    logger.info(f"Base directory: {BASE_DIR}")
    logger.info(f"Screenshots will be saved to: {SCREENSHOT_DIR}")
    logger.info(f"Logs will be saved to: {LOG_DIR}")
    logger.info(f"Page sources will be saved to: {PAGE_SOURCE_DIR}")

    yield

    logger.info("=== Test Session Ending ===")

# ================================
# Browser Fixture
# ================================
@pytest.fixture(scope="module")
def browser(request):
    """Browser fixture with automatic logging and artifact path tracking."""
    # Ensure user_properties exists on the node early
    if not hasattr(request.node, 'user_properties'):
        request.node.user_properties = []

    # Create test-specific log file and get its path
    test_log_handler, log_file_path = add_test_log_handler(request.node)
    # Store log path in the node for later access in makereport
    request.node.user_properties.append(("artifact_log", log_file_path))

    # Handle both Function and Module nodes
    if hasattr(request.node, 'function') and request.node.function:
        test_name = request.node.function.__name__
    else:
        test_name = getattr(request.node, 'name', 'unknown_test')

    logger.info(f"Starting browser for test: {test_name}")

    # Configure Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")

    # Enable detailed performance logging for network metrics
    chrome_options.set_capability("goog:loggingPrefs", {
        'browser': 'ALL',
        'performance': 'ALL',
        'network': 'ALL'
    })

    # Check if headless mode is enabled via environment variable
    if os.environ.get('HEADLESS') == '1':
        logger.info('Running in headless mode')
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')

    driver = None
    try:
        # Create the WebDriver instance
        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=chrome_options
        )

        # Set implicit wait time
        driver.implicitly_wait(10)

        # Initialize performance logs collection
        raw_performance_logs = []
        browser_timing_metrics = {}

        # Store initial performance logs
        try:
            initial_logs = driver.get_log('performance')
            raw_performance_logs.extend(initial_logs)
            logger.info(f"Collected {len(initial_logs)} initial performance logs")
        except Exception as e:
            logger.warning(f"Could not collect initial performance logs: {e}")

        # Store performance data in node properties for later access
        request.node.user_properties.append(("raw_performance_logs", raw_performance_logs))
        request.node.user_properties.append(("browser_timing_metrics", browser_timing_metrics))

        logger.info(f"Browser initialized successfully for test: {test_name}")

        yield driver

        # Teardown: collect final performance logs and metrics
        try:
            # Collect final performance logs
            final_logs = driver.get_log('performance')
            raw_performance_logs.extend(final_logs)
            logger.info(f"Collected {len(final_logs)} final performance logs")

            # Extract browser timing metrics
            browser_timing_metrics = extract_browser_timing_metrics(driver)
            logger.info(f"Extracted browser timing metrics: {browser_timing_metrics}")

            # Update the stored performance data
            for i, (name, value) in enumerate(request.node.user_properties):
                if name == "raw_performance_logs":
                    request.node.user_properties[i] = (name, raw_performance_logs)
                elif name == "browser_timing_metrics":
                    request.node.user_properties[i] = (name, browser_timing_metrics)

        except Exception as e:
            logger.error(f"Error collecting final performance data: {e}")

    except Exception as e:
        logger.error(f"Failed to initialize browser: {e}")
        # Ensure properties are added even if browser init fails
        if not any(name == "raw_performance_logs" for name, _ in request.node.user_properties):
             request.node.user_properties.append(("raw_performance_logs", []))
        if not any(name == "browser_timing_metrics" for name, _ in request.node.user_properties):
             request.node.user_properties.append(("browser_timing_metrics", {}))
        raise # Re-raise the exception to mark the test as failed
    finally:
        try:
            if driver: # Check if driver exists before quitting
                driver.quit()
                logger.info("Browser closed successfully")
        except Exception as e:
            logger.error(f"Failed to close browser: {e}")

# ================================
# Additional Fixtures
# ================================
@pytest.fixture
def take_timed_screenshots(browser):
    """Fixture to allow taking screenshots during test execution."""
    def _take_screenshot(description=""):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        if description:
            filename = f"screenshot_{description}_{timestamp}.png"
        else:
            filename = f"screenshot_{timestamp}.png"

        screenshot_path = SCREENSHOT_DIR / filename
        browser.save_screenshot(str(screenshot_path))
        logger.info(f"Screenshot saved: {screenshot_path}")
        return str(screenshot_path)

    return _take_screenshot

@pytest.fixture
def log_message():
    """Fixture to allow adding custom log messages from tests."""
    def _log_message(message, level="info"):
        if level.lower() == "debug":
            logger.debug(f"TEST MESSAGE: {message}")
        elif level.lower() == "warning":
            logger.warning(f"TEST MESSAGE: {message}")
        elif level.lower() == "error":
            logger.error(f"TEST MESSAGE: {message}")
        else:
            logger.info(f"TEST MESSAGE: {message}")

    return _log_message
#
#  ================================
# Performance Monitoring Fixture
# ================================
@pytest.fixture(autouse=True)
def performance_monitor(request):
    """
    Fixture to monitor test performance including:
    - Test execution time
    - Memory usage
    - CPU usage
    - Network metrics (requests and data transferred)
    - Page load time

    This fixture is automatically applied to all tests (autouse=True)
    You don't need to explicitly include this fixture in your test functions.
    """
    # Log that performance monitoring is active
    logger.info(f"Performance monitoring ACTIVATED for test: {request.node.nodeid}")

    # Start measuring system metrics
    start_time = time.time()
    start_memory = get_memory_usage()

    # Initialize CPU monitoring - reset to get consistent readings
    psutil.cpu_percent(interval=None)  # Initialize CPU monitoring

    # Initialize metrics collection
    metrics = {}

    # Check if browser fixture is active for this test
    has_browser = "browser" in request.fixturenames

    # Create and yield the monitor object
    yield metrics # Test function executes here

    # --- Teardown starts here (after test function completes) ---

    # Calculate basic execution time for the whole test
    duration = time.time() - start_time
    end_memory = get_memory_usage()

    # Get CPU usage - this will be the average since we initialized it above
    cpu_used = psutil.cpu_percent(interval=0.1)  # Get CPU usage since initialization

    # Calculate memory difference
    memory_used = end_memory - start_memory

    # Get browser performance metrics if available
    browser_metrics = {} # This will now be populated from user_properties
    network_metrics = {"network_requests": 0, "network_bytes": 0}

    if has_browser:
        # Retrieve raw performance logs stored by the browser fixture
        raw_performance_logs = next((value for name, value in request.node.user_properties if name == "raw_performance_logs"), [])
        logger.info(f"DEBUG [conftest]: Performance monitor retrieved {len(raw_performance_logs)} raw performance logs from user_properties.")

        # Parse network metrics from the retrieved logs
        network_metrics = parse_network_metrics_from_logs(raw_performance_logs)
        logger.info(f"DEBUG [conftest]: Performance monitor parsed network metrics: {network_metrics}")

        # Retrieve browser timing metrics stored by the browser fixture
        browser_metrics = next((value for name, value in request.node.user_properties if name == "browser_timing_metrics"), {})
        logger.info(f"DEBUG [conftest]: Performance monitor retrieved browser timing metrics from user_properties: {browser_metrics}")

    # Combine system and network metrics
    metrics.update({
        "execution_time": duration,
        "memory_usage": memory_used,
        "cpu_usage": cpu_used,
        "network_requests": network_metrics.get("network_requests", 0),
        "network_bytes": network_metrics.get("network_bytes", 0)
    })

    # Add page load time if available from the retrieved browser_metrics
    if browser_metrics.get('total_page_load'):
        metrics["page_load_time"] = browser_metrics.get('total_page_load')

    # Store performance metrics in the test node for later access
    if not hasattr(request.node, 'user_properties'):
        request.node.user_properties = []

    request.node.user_properties.append(("performance_metrics", metrics))

    logger.info(f"Performance monitoring COMPLETED for test: {request.node.nodeid}")
    logger.info(f"Performance metrics: {metrics}")

# ================================
# Pytest Hooks
# ================================
@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    # --- Artifact Capture (Moved Before Yield) ---
    if call.when == "call": # Check if this is the execution phase
        driver = None
        if "browser" in item.fixturenames:
            try:
                # Access the driver instance that was yielded
                driver = item.funcargs.get("browser")
            except Exception as fixture_exc:
                logger.error(f"Could not retrieve 'browser' fixture for {item.nodeid} before yield: {fixture_exc}")
                driver = None

        if driver:
            failed = call.excinfo is not None
            result_status = "FAIL" if failed else "PASS"
            logger.info(f"Test {result_status.lower()} ({item.nodeid}): Capturing artifacts post-execution, pre-report.")

            if not hasattr(item, 'user_properties'):
                item.user_properties = []

            # Handle both Function and Module nodes
            if hasattr(item, 'function') and item.function:
                test_name = item.function.__name__
                class_name = item.cls.__name__ if item.cls else "nogroup"
            else:
                test_name = getattr(item, 'name', 'unknown_test')
                class_name = "nogroup"

            base_name_pattern = f"{class_name}_{test_name}"
            if class_name == "nogroup":
                base_name_pattern = test_name

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Take screenshot
            try:
                screenshot_filename = f"{base_name_pattern}_{timestamp}.png"
                screenshot_path = SCREENSHOT_DIR / screenshot_filename
                driver.save_screenshot(str(screenshot_path))
                item.user_properties.append(("artifact_screenshot", str(screenshot_path)))
                logger.info(f"Screenshot captured: {screenshot_path}")
            except Exception as screenshot_exc:
                logger.error(f"Failed to capture screenshot for {item.nodeid}: {screenshot_exc}")

            # Save page source
            try:
                page_source_filename = f"{base_name_pattern}_{timestamp}.html"
                page_source_path = PAGE_SOURCE_DIR / page_source_filename
                with open(page_source_path, 'w', encoding='utf-8') as f:
                    f.write(driver.page_source)
                item.user_properties.append(("artifact_page_source", str(page_source_path)))
                logger.info(f"Page source saved: {page_source_path}")
            except Exception as page_source_exc:
                logger.error(f"Failed to save page source for {item.nodeid}: {page_source_exc}")

    # Execute the test and get the report
    outcome = yield
    report = outcome.get_result()

    # Add custom attributes to the report for XML output
    if hasattr(item, 'user_properties'):
        for name, value in item.user_properties:
            if name == "performance_metrics" and isinstance(value, dict):
                # Add performance metrics as properties
                for metric_name, metric_value in value.items():
                    report.user_properties.append((f"perf_{metric_name}", str(metric_value)))
            else:
                report.user_properties.append((name, str(value)))

# ================================
# Terminal Summary Hook
# ================================
@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_terminal_summary(terminalreporter, exitstatus=None, config=None):
    """
    Create a summary report at the end of the test session with enhanced performance metrics.
    """
    # Acknowledge parameters to avoid linter warnings
    _ = exitstatus, config

    yield

    # Collect basic test statistics
    stats = {
        'total': len(terminalreporter.stats.get('passed', [])) +
                len(terminalreporter.stats.get('failed', [])) +
                len(terminalreporter.stats.get('skipped', [])),
        'passed': len(terminalreporter.stats.get('passed', [])),
        'failed': len(terminalreporter.stats.get('failed', [])),
        'skipped': len(terminalreporter.stats.get('skipped', [])),
        'duration': time.time() - terminalreporter._sessionstarttime
    }

    # Collect performance metrics from all tests
    test_metrics = {}
    metric_count = 0

    # Process all test reports to extract performance metrics
    for test_reports in [terminalreporter.stats.get('passed', []),
                        terminalreporter.stats.get('failed', []),
                        terminalreporter.stats.get('skipped', [])]:
        for report in test_reports:
            if hasattr(report, 'user_properties'):
                test_id = report.nodeid
                test_metrics[test_id] = {}

                for name, value in report.user_properties:
                    if name.startswith('perf_'):
                        metric_name = name[5:]  # Remove 'perf_' prefix
                        try:
                            test_metrics[test_id][metric_name] = float(value)
                            metric_count += 1
                        except (ValueError, TypeError):
                            test_metrics[test_id][metric_name] = value

    # Calculate aggregate performance metrics
    if test_metrics:
        aggregate_metrics = {}
        metric_names = set()

        # Collect all metric names
        for test_data in test_metrics.values():
            metric_names.update(test_data.keys())

        # Calculate averages for numeric metrics
        for metric_name in metric_names:
            values = []
            for test_data in test_metrics.values():
                if metric_name in test_data:
                    try:
                        values.append(float(test_data[metric_name]))
                    except (ValueError, TypeError):
                        pass

            if values:
                aggregate_metrics[f'avg_{metric_name}'] = sum(values) / len(values)
                aggregate_metrics[f'max_{metric_name}'] = max(values)
                aggregate_metrics[f'min_{metric_name}'] = min(values)

        stats['performance_metrics'] = {
            'individual_tests': test_metrics,
            'aggregate': aggregate_metrics,
            'metric_count': metric_count
        }

    # Display summary in terminal
    terminalreporter.write_sep("=", "Test Execution Summary")
    terminalreporter.write_line(f"Total tests: {stats['total']}")
    terminalreporter.write_line(f"Passed: {stats['passed']}")
    terminalreporter.write_line(f"Failed: {stats['failed']}")
    terminalreporter.write_line(f"Skipped: {stats['skipped']}")
    terminalreporter.write_line(f"Duration: {stats['duration']:.2f} seconds")

    if metric_count > 0:
        terminalreporter.write_line(f"Performance metrics collected: {metric_count}")

        # Display aggregate performance metrics
        if 'performance_metrics' in stats and 'aggregate' in stats['performance_metrics']:
            terminalreporter.write_sep("-", "Performance Summary")
            for metric_name, value in stats['performance_metrics']['aggregate'].items():
                if isinstance(value, float):
                    terminalreporter.write_line(f"{metric_name}: {value:.3f}")
                else:
                    terminalreporter.write_line(f"{metric_name}: {value}")

    # Add performance metrics to the XML file
    if metric_count > 0:
        try:
            # Find the XML file
            xml_files = list(Path(os.getcwd()).glob("results_*.xml"))
            if xml_files:
                latest_xml = max(xml_files, key=os.path.getmtime)
                add_performance_metrics_to_xml(latest_xml, test_metrics)
        except Exception as e:
            logger.error(f"Failed to add performance metrics to XML file: {e}")
            logger.error(traceback.format_exc())

    # Save detailed summary to JSON file
    summary_file = LOG_DIR / f"test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(summary_file, 'w') as f:
        json.dump(stats, f, indent=2)
    logger.info(f"Test summary saved to {summary_file}")

# ================================
# XML Processing Functions
# ================================
def add_performance_metrics_to_xml(xml_file_path, test_metrics):
    """
    Add performance metrics to the JUnit XML file.

    Args:
        xml_file_path: Path to the XML file
        test_metrics: Dictionary of test metrics
    """
    try:
        # Parse the XML file
        tree = ET.parse(xml_file_path)
        root = tree.getroot()

        # Find all testcase elements and add performance metrics
        for testcase in root.findall('.//testcase'):
            classname = testcase.get('classname', '')
            name = testcase.get('name', '')

            # Construct the test ID to match with our metrics
            test_id = f"{classname}::{name}" if classname else name

            # Look for matching metrics (try different formats)
            metrics = None
            for stored_test_id, stored_metrics in test_metrics.items():
                if (test_id in stored_test_id or
                    stored_test_id.endswith(f"::{name}") or
                    stored_test_id.endswith(name)):
                    metrics = stored_metrics
                    break

            if metrics:
                # Add performance metrics as properties
                properties = testcase.find('properties')
                if properties is None:
                    properties = ET.SubElement(testcase, 'properties')

                for metric_name, metric_value in metrics.items():
                    property_elem = ET.SubElement(properties, 'property')
                    property_elem.set('name', f'perf_{metric_name}')
                    property_elem.set('value', str(metric_value))

        # Write the modified XML back to file
        tree.write(xml_file_path, encoding='utf-8', xml_declaration=True)
        logger.info(f"Performance metrics added to XML file: {xml_file_path}")

    except Exception as e:
        logger.error(f"Error adding performance metrics to XML: {e}")
        logger.error(traceback.format_exc())

# ================================
# Compatibility Aliases
# ================================
# Provide compatibility with existing test scripts that might use 'driver' fixture
@pytest.fixture
def driver(browser):
    """Compatibility alias for the browser fixture."""
    return browser
