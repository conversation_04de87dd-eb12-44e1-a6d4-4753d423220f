"""
Stage 3: Test Case Analysis and Conversion

This module handles test case selection, analysis, and conversion to automation format.
Maintains the StateManager pattern and follows the established architectural patterns.
"""

import json
import logging
import streamlit as st
import re
from typing import Dict, List, Tuple, Optional

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage3")

# Import helper functions from other modules
from core.ai import convert_test_case_to_step_table, generate_llm_response
from core.ai_helpers import analyze_step_table
from core.analysis import validate_test_case_structure
from state_manager import StateStage
from debug_utils import debug

def stage3_convert_test_case(state):
    """Phase 3: Test Case Analysis and Conversion."""
    st.markdown("<h2 class='stage-header'>Phase 3: Test Case Analysis</h2>", unsafe_allow_html=True)

    # Display conversion success message if available (from previous conversion)
    if 'conversion_success_message' in st.session_state:
        st.success(st.session_state['conversion_success_message'])
        # Remove the message so it doesn't show again
        del st.session_state['conversion_success_message']

    # Initialize state variables for step-by-step workflow
    if not hasattr(state, 'selected_test_case') or state.selected_test_case is None:
        state.selected_test_case = None
    if not hasattr(state, 'selected_step') or state.selected_step is None:
        state.selected_step = None
    if not hasattr(state, 'step_elements') or state.step_elements is None:
        state.step_elements = []
    if not hasattr(state, 'step_matches') or state.step_matches is None:
        state.step_matches = {}

    # Test Case Selection Section
    st.markdown("#### Select Test Case")

    # Add test case selection dropdown
    selected_test_case = None
    if state.test_cases:
        # Defensive programming: validate test_cases format
        if not isinstance(state.test_cases, list):
            st.error(f"❌ Invalid test case format: expected list, got {type(state.test_cases).__name__}")
            debug(f"Invalid test_cases format: {type(state.test_cases)}")
            return

        if not state.test_cases:
            st.warning("⚠️ No test cases available")
            debug("Empty test_cases list")
            return

        test_case_options = []
        for i, tc in enumerate(state.test_cases):
            if isinstance(tc, dict):
                tc_id = tc.get('Test Case ID', '')
                objective = tc.get('Test Case Objective', '')
                if tc_id and objective:
                    # Truncate objective to prevent UI issues
                    truncated_objective = objective[:50] + "..." if len(objective) > 50 else objective
                    test_case_options.append(f"{tc_id} - {truncated_objective}")
                else:
                    debug(f"Skipping test case {i}: missing ID or objective (ID: '{tc_id}', Objective: '{objective}')")
            else:
                debug(f"Skipping test case {i}: not a dictionary (type: {type(tc)})")

        if test_case_options:
            selected_option = st.selectbox(
                "Test Case",
                ["Select a test case..."] + test_case_options
            )
        else:
            st.warning("⚠️ No valid test cases found with required ID and Objective fields")
            debug("No valid test case options could be built from available test cases")
            return

        if selected_option != "Select a test case...":
            # Defensive programming: validate selected_option format
            if not selected_option or " - " not in selected_option:
                st.error("❌ Invalid test case selection format")
                debug(f"Invalid test case selection: {selected_option}")
                return

            try:
                selected_tc_id = selected_option.split(" - ")[0]
            except (IndexError, AttributeError) as e:
                st.error("❌ Error parsing test case selection")
                debug(f"Error parsing test case selection: {e}")
                return

            selected_test_case = next(
                (tc for tc in state.test_cases if tc.get('Test Case ID') == selected_tc_id),
                None
            )

            if selected_test_case:
                # Check if this is a different test case than the currently selected one
                is_new_test_case = (not hasattr(state, 'selected_test_case') or
                                   not state.selected_test_case or
                                   state.selected_test_case.get('Test Case ID') != selected_test_case.get('Test Case ID'))

                if is_new_test_case:
                    # Ask for confirmation if we already have a test case with progress
                    has_progress = (hasattr(state, 'step_table_json') and state.step_table_json and
                                   (hasattr(state, 'current_step_index') and state.current_step_index > 0))

                    if has_progress:
                        with st.expander("⚠️ Warning: Progress Will Be Reset", expanded=True):
                            st.warning("Changing test cases will reset all progress on the current test case.")
                            confirm = st.button("Confirm Change", key="confirm_test_case_change")

                            if not confirm:
                                st.info("Select the same test case to continue with your current progress.")
                                return

                    # Reset test case state with confirmation
                    state.reset_test_case_state(confirm=True, reason=f"New test case selected: {selected_test_case.get('Test Case ID')}")

                # Validate test case structure before storing
                validation_result = validate_test_case_structure(selected_test_case)

                if not validation_result['valid']:
                    st.warning("⚠️ Test case structure issues detected:")
                    for warning in validation_result['warnings']:
                        st.warning(f"• {warning}")

                    if validation_result['corrections']:
                        with st.expander("Recommended Corrections", expanded=False):
                            for correction in validation_result['corrections'].values():
                                st.info(f"• {correction}")

                # Store the original test case in state manager before conversion
                state.selected_test_case = selected_test_case
                state.original_test_case = selected_test_case.copy() if isinstance(selected_test_case, dict) else selected_test_case

                # Display test case details in a collapsible section
                with st.expander("Test Case Details", expanded=True):
                    test_case_details_col1, test_case_details_col2 = st.columns(2)
                    with test_case_details_col1:
                        st.markdown(f"**ID:** {selected_test_case.get('Test Case ID')}")
                        st.markdown(f"**Steps:** {len(selected_test_case.get('Steps', []))}")
                    with test_case_details_col2:
                        st.markdown(f"**Objective:** {selected_test_case.get('Test Case Objective')}")

                # Check if conversion has already been done
                if hasattr(state, 'step_table_markdown') and state.step_table_markdown and hasattr(state, 'conversion_done') and state.conversion_done:
                    _display_converted_test_case(state)
                else:
                    _display_conversion_section(state, selected_test_case)

                # Check if step table conversion is complete before proceeding
                if not (hasattr(state, 'step_table_markdown') and state.step_table_markdown and hasattr(state, 'conversion_done') and state.conversion_done):
                    with st.expander("Next Steps", expanded=True):
                        st.info("ℹ️ Please convert the test case to continue to Phase 4")

def _display_converted_test_case(state):
    """Display the converted test case with analysis results."""
    st.success("✅ Test case converted to automation format")

    # Display AI-powered completeness validation results
    if hasattr(state, 'completeness_validation') and state.completeness_validation:
        _display_completeness_validation_results(state.completeness_validation)

    # Display UI element detection recommendation in an expander
    if hasattr(state, 'step_table_analysis') and state.step_table_analysis:
        with st.expander("UI Element Analysis", expanded=False):
            step_table_analysis = state.step_table_analysis
            if step_table_analysis.get("requires_ui_elements", True):
                st.info(f"🔍 UI Element Detection Recommended: {step_table_analysis.get('reason', 'For proper automation')}")
            else:
                st.success(f"✅ UI Element Detection Not Needed: {step_table_analysis.get('reason', 'No UI elements required')}")

    # Show the converted step table in an expander
    with st.expander("View Converted Step Table", expanded=True):
        # Display the step table in a more interactive way
        tab1, tab2, tab3 = st.tabs(["Markdown Format", "JSON Data", "Comparison"])

        with tab1:
            st.markdown(state.step_table_markdown)
            # Add a button to copy the step table to clipboard
            if st.button("Copy Markdown", key="copy_step_table_btn", help="Copy to clipboard"):
                try:
                    import pyperclip
                    pyperclip.copy(state.step_table_markdown)
                    st.success("✅ Copied to clipboard")
                except ImportError:
                    st.warning("⚠️ pyperclip module not installed")
                except Exception as e:
                    st.error(f"❌ Error: {e}")

        with tab2:
            # Use st.json for better formatting and interaction
            st.json(state.step_table_json)
            # Add a button to copy the JSON to clipboard
            if st.button("Copy JSON", key="copy_json_btn", help="Copy to clipboard"):
                try:
                    import pyperclip
                    pyperclip.copy(json.dumps(state.step_table_json, indent=2))
                    st.success("✅ Copied to clipboard")
                except ImportError:
                    st.warning("⚠️ pyperclip module not installed")
                except Exception as e:
                    st.error(f"❌ Error: {e}")

        with tab3:
            # Show comparison between original and converted test case
            # This was moved here from the conversion flow to prevent UI flashing
            if hasattr(state, 'selected_test_case') and state.selected_test_case:
                comp_col1, comp_col2 = st.columns(2)
                with comp_col1:
                    st.markdown("**Original Format**")
                    original_steps = state.selected_test_case.get('Steps', [])
                    if original_steps:
                        for step in original_steps:
                            st.markdown(f"**Step {step.get('Step No')}:** {step.get('Test Steps')}")
                            st.markdown(f"*Expected:* {step.get('Expected Result')}")
                            st.markdown("---")
                    else:
                        st.info("No original steps available")

                with comp_col2:
                    st.markdown("**Automation Format**")
                    st.markdown(state.step_table_markdown)
            else:
                st.info("Original test case not available for comparison")

    # Hybrid Editing Section - Only show if conversion is done
    if state.conversion_done and state.step_table_json:
        st.markdown("---")
        _display_hybrid_editing_section(state)

        # Auto-sync combined steps if hybrid editing is enabled and has manual steps
        if (state.hybrid_editing_enabled and
            hasattr(state, 'step_insertion_points') and
            state.step_insertion_points and
            hasattr(state, 'sync_step_table_with_combined')):
            state.sync_step_table_with_combined()

    # Add button to re-convert if needed
    if st.button("Re-Convert Test Case", key="reconvert_to_step_table_btn"):
        state.conversion_done = False
        st.rerun()


def _display_completeness_validation_results(validation_results):
    """
    Display AI-powered test case completeness validation results in a user-friendly format.

    Args:
        validation_results (dict): Results from AI completeness validation
    """
    # Get overall score and status
    score = validation_results.get('overall_completeness_score', 0)
    is_complete = validation_results.get('is_complete', False)
    summary = validation_results.get('summary', 'No summary available')

    # Determine score color and icon
    if score >= 8:
        score_color = "🟢"
        status_type = "success"
    elif score >= 6:
        score_color = "🟡"
        status_type = "warning"
    else:
        score_color = "🔴"
        status_type = "error"

    # Main completeness status
    with st.expander(f"🤖 AI Completeness Analysis {score_color} Score: {score}/10", expanded=True):
        # Overall status
        if status_type == "success":
            st.success(f"✅ **Test Case Status**: {summary}")
        elif status_type == "warning":
            st.warning(f"⚠️ **Test Case Status**: {summary}")
        else:
            st.error(f"❌ **Test Case Status**: {summary}")

        # Detailed validation results
        validation_data = validation_results.get('validation_results', {})

        # Create tabs for different validation categories
        nav_tab, flow_tab, e2e_tab, gaps_tab = st.tabs([
            "🧭 Navigation",
            "🔄 Step Flow",
            "🎯 End-to-End",
            "⚠️ Critical Gaps"
        ])

        # Navigation validation
        with nav_tab:
            nav_data = validation_data.get('navigation', {})
            has_nav = nav_data.get('has_navigation', True)

            if has_nav:
                st.success("✅ Navigation steps detected")
            else:
                st.error("❌ Missing navigation steps")

            # Navigation issues
            nav_issues = nav_data.get('issues', [])
            if nav_issues:
                st.markdown("**Issues Found:**")
                for issue in nav_issues:
                    st.error(f"• {issue}")

            # Navigation recommendations
            nav_recommendations = nav_data.get('recommendations', [])
            if nav_recommendations:
                st.markdown("**Recommendations:**")
                for rec in nav_recommendations:
                    st.info(f"💡 {rec}")

        # Step flow validation
        with flow_tab:
            flow_data = validation_data.get('step_flow', {})
            is_logical = flow_data.get('is_logical', True)

            if is_logical:
                st.success("✅ Step sequence is logical")
            else:
                st.warning("⚠️ Step sequence issues detected")

            # Missing steps
            missing_steps = flow_data.get('missing_steps', [])
            if missing_steps:
                st.markdown("**Missing Steps:**")
                for step in missing_steps:
                    st.warning(f"• {step}")

            # Sequence issues
            sequence_issues = flow_data.get('sequence_issues', [])
            if sequence_issues:
                st.markdown("**Sequence Issues:**")
                for issue in sequence_issues:
                    st.error(f"• {issue}")

            # Flow recommendations
            flow_recommendations = flow_data.get('recommendations', [])
            if flow_recommendations:
                st.markdown("**Recommendations:**")
                for rec in flow_recommendations:
                    st.info(f"💡 {rec}")

        # End-to-end validation
        with e2e_tab:
            e2e_data = validation_data.get('end_to_end', {})
            has_completion = e2e_data.get('has_completion', True)
            has_verification = e2e_data.get('has_verification', True)

            col1, col2 = st.columns(2)
            with col1:
                if has_completion:
                    st.success("✅ Completion steps present")
                else:
                    st.error("❌ Missing completion steps")

            with col2:
                if has_verification:
                    st.success("✅ Verification steps present")
                else:
                    st.error("❌ Missing verification steps")

            # Missing assertions
            missing_assertions = e2e_data.get('missing_assertions', [])
            if missing_assertions:
                st.markdown("**Missing Assertions:**")
                for assertion in missing_assertions:
                    st.warning(f"• {assertion}")

            # E2E recommendations
            e2e_recommendations = e2e_data.get('recommendations', [])
            if e2e_recommendations:
                st.markdown("**Recommendations:**")
                for rec in e2e_recommendations:
                    st.info(f"💡 {rec}")

        # Critical gaps
        with gaps_tab:
            critical_gaps = validation_data.get('critical_gaps', [])

            if not critical_gaps:
                st.success("✅ No critical gaps detected")
            else:
                st.error("❌ Critical gaps found that may cause automation failures:")
                for gap in critical_gaps:
                    st.error(f"🚨 {gap}")

        # Priority improvements
        priority_improvements = validation_results.get('priority_improvements', [])
        if priority_improvements:
            st.markdown("---")
            st.markdown("**🎯 Priority Improvements:**")
            for i, improvement in enumerate(priority_improvements, 1):
                st.info(f"{i}. {improvement}")

        # Intelligent Regeneration Section - Button only inside expander
        if score < 8 or not is_complete:
            st.markdown("---")
            st.markdown("### 🤖 Intelligent Issue Resolution")

            # Trigger issue classification if not already done
            if not hasattr(st.session_state, 'issue_classification'):
                if st.button("🔍 Analyze Issues", key="classify_issues", help="Classify issues as auto-fixable or requiring regeneration"):
                    with st.spinner("Analyzing issues..."):
                        # Get the current state to access test case and API key
                        state = st.session_state.state
                        classification_results = classify_validation_issues_with_ai(
                            validation_results,
                            state.selected_test_case,
                            state.google_api_key
                        )
                        st.session_state['issue_classification'] = classification_results
                        st.rerun()
            else:
                st.info("🔍 Issues analyzed. See resolution options below.")

        # Action buttons
        st.markdown("---")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 Re-validate", key="revalidate_completeness", help="Run AI validation again"):
                # Clear validation results to trigger re-validation
                if hasattr(st.session_state.state, 'completeness_validation'):
                    delattr(st.session_state.state, 'completeness_validation')
                if 'issue_classification' in st.session_state:
                    del st.session_state['issue_classification']
                st.rerun()

        with col2:
            if not is_complete and score < 6:
                if st.button("⚠️ Proceed Anyway", key="proceed_despite_issues", help="Continue despite validation issues"):
                    st.warning("⚠️ Proceeding with incomplete test case. Manual review recommended.")

        with col3:
            if st.button("📋 Export Report", key="export_validation_report", help="Export validation report"):
                # Create a downloadable validation report
                report_content = f"""
AI Test Case Completeness Validation Report
==========================================

Test Case: {validation_results.get('test_case_id', 'Unknown')}
Overall Score: {score}/10
Status: {'Complete' if is_complete else 'Incomplete'}

Summary: {summary}

Priority Improvements:
{chr(10).join(f"- {imp}" for imp in priority_improvements)}

Detailed Analysis:
- Navigation: {'✅' if nav_data.get('has_navigation') else '❌'}
- Step Flow: {'✅' if flow_data.get('is_logical') else '⚠️'}
- End-to-End: {'✅' if e2e_data.get('has_completion') and e2e_data.get('has_verification') else '❌'}
- Critical Gaps: {len(critical_gaps)} found

Generated by GretahAI ScriptWeaver
"""
                st.download_button(
                    label="📥 Download Report",
                    data=report_content,
                    file_name=f"validation_report_{validation_results.get('test_case_id', 'unknown')}.txt",
                    mime="text/plain"
                )

    # Display issue resolution options OUTSIDE the main expander to avoid nesting
    if (score < 8 or not is_complete) and hasattr(st.session_state, 'issue_classification'):
        classification = st.session_state['issue_classification']
        _display_issue_resolution_options(classification, validation_results)


def _display_issue_resolution_options(classification_results: Dict, validation_results: Dict):
    """
    Display intelligent issue resolution options based on AI classification.

    Args:
        classification_results (Dict): Results from issue classification
        validation_results (Dict): Original validation results
    """
    solvable_issues = classification_results.get('solvable_issues', [])
    unsolvable_issues = classification_results.get('unsolvable_issues', [])
    recommended_action = classification_results.get('recommended_action', 'manual_edit')

    # Display classification summary
    st.info(f"🔍 **Analysis Complete**: {classification_results.get('classification_summary', 'No summary available')}")

    # Show issue breakdown
    col1, col2 = st.columns(2)

    with col1:
        if solvable_issues:
            st.success(f"✅ **Auto-Fixable Issues**: {len(solvable_issues)}")
            # Use details without nested expanders
            if st.checkbox("Show Auto-Fixable Details", key="show_solvable_details"):
                for i, issue in enumerate(solvable_issues):
                    st.markdown(f"**{i+1}. {issue.get('category', 'Unknown')}**: {issue.get('issue', 'No description')}")
                    st.markdown(f"*Fix*: {issue.get('auto_fix_suggestion', 'No suggestion')}")
                    st.markdown(f"*Confidence*: {issue.get('confidence', 0)}/10")
                    if i < len(solvable_issues) - 1:
                        st.markdown("---")
        else:
            st.info("ℹ️ **Auto-Fixable Issues**: None detected")

    with col2:
        if unsolvable_issues:
            st.warning(f"⚠️ **Regeneration Required**: {len(unsolvable_issues)}")
            # Use details without nested expanders
            if st.checkbox("Show Complex Issue Details", key="show_unsolvable_details"):
                for i, issue in enumerate(unsolvable_issues):
                    st.markdown(f"**{i+1}. {issue.get('category', 'Unknown')}**: {issue.get('issue', 'No description')}")
                    st.markdown(f"*Reason*: {issue.get('regeneration_reason', 'No reason')}")
                    st.markdown(f"*Complexity*: {issue.get('complexity', 'Unknown')}")
                    if i < len(unsolvable_issues) - 1:
                        st.markdown("---")
        else:
            st.success("✅ **Complex Issues**: None detected")

    # Action recommendations
    st.markdown("### 🎯 Recommended Actions")

    if recommended_action == "auto_fix":
        st.success("🔧 **Recommended**: Apply automatic fixes")
        if st.button("🔧 Apply Auto-Fixes", key="apply_auto_fixes", help="Automatically fix solvable issues"):
            _handle_auto_fix_action(solvable_issues)

    elif recommended_action == "regenerate":
        st.warning("🔄 **Recommended**: Complete regeneration required")
        if st.button("🔄 Regenerate Test Case", key="regenerate_test_case", help="Intelligently regenerate the entire test case"):
            _handle_regeneration_action(unsolvable_issues, validation_results)

    elif recommended_action == "hybrid":
        st.info("🔀 **Recommended**: Hybrid approach (auto-fix + regeneration)")

        action_col1, action_col2 = st.columns(2)
        with action_col1:
            if st.button("🔧 Apply Auto-Fixes First", key="apply_fixes_first", help="Fix solvable issues first"):
                _handle_auto_fix_action(solvable_issues)

        with action_col2:
            if st.button("🔄 Full Regeneration", key="full_regeneration", help="Regenerate entire test case"):
                _handle_regeneration_action(unsolvable_issues, validation_results)

    else:  # manual_edit
        st.info("✏️ **Recommended**: Manual editing")
        st.markdown("Consider manually editing the test case or using the re-conversion option.")


def _handle_auto_fix_action(solvable_issues: List[Dict]):
    """Handle the auto-fix action for solvable issues."""
    with st.spinner("Applying automatic fixes..."):
        state = st.session_state.state

        # Apply auto-fixes
        fixed_markdown, fixed_json = apply_auto_fixes_with_ai(
            state.step_table_json,
            solvable_issues,
            state.google_api_key
        )

        if fixed_markdown and fixed_json:
            # Update state with fixed results
            state.step_table_markdown = fixed_markdown
            state.step_table_json = fixed_json

            # Clear validation results to trigger re-validation
            if hasattr(state, 'completeness_validation'):
                delattr(state, 'completeness_validation')
            if 'issue_classification' in st.session_state:
                del st.session_state['issue_classification']

            st.success("✅ Auto-fixes applied successfully! Re-validating...")
            st.rerun()
        else:
            st.error("❌ Auto-fix failed. Please try manual editing or regeneration.")


def _handle_regeneration_action(unsolvable_issues: List[Dict], validation_results: Dict):
    """Handle the regeneration action for complex issues."""
    # Show user comment input for regeneration
    st.markdown("### 💬 Additional Requirements")
    user_comments = st.text_area(
        "Provide additional requirements or comments for regeneration:",
        placeholder="e.g., 'Add error handling steps', 'Include data validation', 'Focus on mobile testing'...",
        key="regeneration_comments"
    )

    if st.button("🚀 Start Regeneration", key="confirm_regeneration", help="Begin intelligent test case regeneration"):
        with st.spinner("Regenerating test case with AI..."):
            state = st.session_state.state

            # Create classification results for regeneration
            classification_results = {
                'unsolvable_issues': unsolvable_issues,
                'regeneration_required': True
            }

            # Regenerate test case
            regenerated_markdown, regenerated_json = regenerate_test_case_with_ai(
                state.selected_test_case,
                validation_results,
                classification_results,
                user_comments,
                state.google_api_key
            )

            if regenerated_markdown and regenerated_json:
                # Update state with regenerated results
                state.step_table_markdown = regenerated_markdown
                state.step_table_json = regenerated_json

                # Clear validation results to trigger re-validation
                if hasattr(state, 'completeness_validation'):
                    delattr(state, 'completeness_validation')
                if 'issue_classification' in st.session_state:
                    del st.session_state['issue_classification']

                st.success("✅ Test case regenerated successfully! Re-validating...")
                st.rerun()
            else:
                st.error("❌ Regeneration failed. Please try manual editing or contact support.")


def _display_conversion_section(state, selected_test_case):
    """Display the conversion section for test case to automation format."""
    # Conversion section
    st.markdown("#### Convert to Automation Format")

    # Add button to convert test case
    convert_button = st.button("🔄 Convert Test Case",
                            key="convert_to_step_table_btn",
                            help="Convert to automation-ready format")

    if convert_button:
        with st.spinner("Converting test case..."):
            try:
                # Use the selected test case for conversion
                markdown_table, json_table = convert_test_case_to_step_table(
                    selected_test_case,
                    state.google_api_key
                )

                # Validate conversion results
                if not markdown_table or not json_table:
                    raise ValueError("Conversion returned empty results")

                if not isinstance(json_table, list):
                    raise ValueError("Invalid JSON table format returned from conversion")

                # Store both the markdown table and JSON table in state manager
                state.step_table_markdown = markdown_table
                state.step_table_json = json_table
                state.conversion_done = True

                # Initialize total_steps from the converted step table
                if json_table and isinstance(json_table, list):
                    state.total_steps = len(json_table)
                    debug(f"State change: total_steps = {state.total_steps} (initialized from step table conversion)")

                # Analyze the step table to determine if UI element detection is needed
                step_table_analysis = analyze_step_table((markdown_table, json_table))
                state.step_table_analysis = step_table_analysis

                # Perform AI-powered completeness validation
                debug("Starting AI-powered test case completeness validation")
                completeness_validation = validate_test_case_completeness_with_ai(
                    selected_test_case,
                    json_table,
                    state.google_api_key
                )
                state.completeness_validation = completeness_validation
                debug(f"AI validation completed with score: {completeness_validation.get('overall_completeness_score', 'N/A')}")

                # Store conversion success message with validation score in session state
                validation_score = completeness_validation.get('overall_completeness_score', 0)
                score_emoji = "🟢" if validation_score >= 8 else "🟡" if validation_score >= 6 else "🔴"
                st.session_state['conversion_success_message'] = f"✅ Test case '{selected_test_case.get('Test Case ID', 'Unknown')}' converted successfully {score_emoji} (Completeness: {validation_score}/10)"

                # Advance to Stage 4 using centralized stage management with immediate rerun
                # This prevents any UI flashing by avoiding rendering elements after stage transition
                debug(f"Advancing to Stage 4 after successful conversion of test case: {selected_test_case.get('Test Case ID', 'Unknown')}")
                state.advance_to(StateStage.STAGE4_DETECT, f"Test case conversion completed for {selected_test_case.get('Test Case ID', 'Unknown')}")
                st.rerun()
                return

            except ValueError as e:
                st.error(f"❌ Validation Error: {e}")
                debug(f"Validation error during conversion: {e}")
            except ConnectionError as e:
                st.error("❌ Network Error: Unable to connect to AI service. Please check your internet connection.")
                debug(f"Connection error during conversion: {e}")
            except TimeoutError as e:
                st.error("❌ Timeout Error: AI service took too long to respond. Please try again.")
                debug(f"Timeout error during conversion: {e}")
            except Exception as e:
                # Log the full error for debugging
                logger.error(f"Unexpected error during test case conversion: {e}")
                debug(f"Unexpected conversion error: {e}")
                st.error("❌ Conversion failed due to an unexpected error. Please try again or contact support if the issue persists.")

                # Provide fallback options
                st.info("💡 **Troubleshooting Tips:**")
                st.info("• Check your internet connection")
                st.info("• Verify your Google API key is valid")


def _display_hybrid_editing_section(state):
    """
    Display the hybrid editing section for AI-assisted test case editing.

    Args:
        state: StateManager instance
    """
    st.markdown("### 🔀 Hybrid Test Case Editing")
    st.markdown("Enhance your AI-generated test case by adding manual steps at key points while preserving AI optimization.")

    # Check if hybrid editing is enabled
    if not state.hybrid_editing_enabled:
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            st.info("🤖 **AI-generated steps are locked** to preserve optimization. Enable hybrid editing to add manual steps.")

        with col2:
            if st.button("🔀 Enable Hybrid Editing", key="enable_hybrid_editing", help="Enable manual step additions"):
                if state.enable_hybrid_editing():
                    st.success("✅ Hybrid editing enabled!")
                    st.rerun()
                else:
                    st.error("❌ Failed to enable hybrid editing")

        with col3:
            if st.button("📖 Learn More", key="hybrid_editing_help", help="Learn about hybrid editing"):
                with st.expander("🔀 Hybrid Editing Help", expanded=True):
                    st.markdown("""
                    **Hybrid Editing** allows you to:

                    🔒 **Preserve AI Steps**: AI-generated steps remain locked and optimized

                    ➕ **Add Manual Steps**: Insert custom steps at any point:
                    - **Navigation steps** before the main test sequence
                    - **Verification steps** for additional checks
                    - **Wait steps** for timing control
                    - **Cleanup steps** at the end

                    🎯 **Smart Insertion**: Choose exactly where to add steps:
                    - At the start of the test
                    - Before/after any AI step
                    - At the end of the test

                    ✅ **Flow Validation**: Automatic validation ensures logical test flow

                    📋 **Templates**: Use predefined templates or create custom steps
                    """)
        return

    # Hybrid editing is enabled - show the editor
    try:
        from ui_components.hybrid_step_editor import render_hybrid_step_editor

        # Render the hybrid step editor
        steps_modified = render_hybrid_step_editor(state)

        if steps_modified:
            st.rerun()

    except ImportError as e:
        st.error(f"❌ Failed to load hybrid step editor: {e}")
        debug(f"Import error for hybrid step editor: {e}")

    # Control buttons
    st.markdown("---")
    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        if st.button("🔄 Reset Manual Steps", key="reset_manual_steps", help="Clear all manual steps"):
            state.step_insertion_points.clear()
            st.success("✅ Manual steps cleared")
            st.rerun()

    with col2:
        if st.button("🔒 Disable Hybrid Editing", key="disable_hybrid_editing", help="Disable hybrid editing mode"):
            state.disable_hybrid_editing()
            st.success("✅ Hybrid editing disabled")
            st.rerun()

    with col3:
        # Show step counts
        ai_count = len(state.ai_generated_steps) if state.ai_generated_steps else 0
        manual_count = sum(len(steps) for steps in state.step_insertion_points.values())
        st.metric("Steps", f"{ai_count + manual_count}", delta=f"+{manual_count} manual")


def validate_test_case_completeness_with_ai(test_case, step_table_json, api_key=None):
    """
    Use AI to validate test case completeness and provide intelligent feedback.

    This function analyzes the converted test case to ensure it includes:
    - Proper navigation steps
    - Complete test flow from start to finish
    - Logical step sequencing
    - Appropriate verification/assertion steps
    - End-to-end coverage

    Args:
        test_case (dict): Original test case data
        step_table_json (list): Converted automation-ready step table
        api_key (str, optional): Google AI API key

    Returns:
        dict: Validation results with completeness analysis
    """
    try:
        debug("Starting AI-powered test case completeness validation")

        # Prepare test case data for AI analysis
        test_case_summary = {
            'id': test_case.get('Test Case ID', 'Unknown'),
            'objective': test_case.get('Test Case Objective', ''),
            'original_steps': [
                {
                    'step_no': step.get('Step No', ''),
                    'action': step.get('Test Steps', ''),
                    'expected': step.get('Expected Result', '')
                }
                for step in test_case.get('Steps', [])
            ],
            'converted_steps': [
                {
                    'step_no': step.get('step_no', ''),
                    'action': step.get('action', ''),
                    'locator_strategy': step.get('locator_strategy', ''),
                    'expected_result': step.get('expected_result', ''),
                    'assertion_type': step.get('assertion_type', '')
                }
                for step in step_table_json
            ]
        }

        # Create comprehensive AI prompt for test case completeness validation
        validation_prompt = f"""
Analyze this test case for completeness and provide specific feedback on missing or inadequate steps.

TEST CASE ANALYSIS:
Test Case ID: {test_case_summary['id']}
Objective: {test_case_summary['objective']}

ORIGINAL STEPS:
{json.dumps(test_case_summary['original_steps'], indent=2)}

CONVERTED AUTOMATION STEPS:
{json.dumps(test_case_summary['converted_steps'], indent=2)}

VALIDATION REQUIREMENTS:
Analyze the test case for the following completeness criteria:

1. NAVIGATION VALIDATION:
   - Does the test case start with proper navigation (URL, login, page access)?
   - Are there clear entry points to begin the test?

2. STEP COMPLETENESS:
   - Are all intermediate steps present and logically sequenced?
   - Do steps flow naturally from one to the next?
   - Are there any obvious gaps in the test flow?

3. END-TO-END COVERAGE:
   - Does the test case include proper completion/verification steps?
   - Are there adequate assertion/verification steps throughout?
   - Does the test case achieve its stated objective?

4. MISSING STEP DETECTION:
   - Identify any critical missing steps that could cause automation failures
   - Detect logical gaps between steps
   - Find missing setup or teardown steps

RESPONSE FORMAT:
Return ONLY valid JSON in this exact format:
{{
    "overall_completeness_score": <integer 1-10>,
    "is_complete": <boolean>,
    "validation_results": {{
        "navigation": {{
            "has_navigation": <boolean>,
            "issues": [<list of navigation issues>],
            "recommendations": [<list of navigation recommendations>]
        }},
        "step_flow": {{
            "is_logical": <boolean>,
            "missing_steps": [<list of missing steps>],
            "sequence_issues": [<list of sequence problems>],
            "recommendations": [<list of flow recommendations>]
        }},
        "end_to_end": {{
            "has_completion": <boolean>,
            "has_verification": <boolean>,
            "missing_assertions": [<list of missing verification steps>],
            "recommendations": [<list of completion recommendations>]
        }},
        "critical_gaps": [<list of critical missing elements>]
    }},
    "summary": "<brief summary of completeness status>",
    "priority_improvements": [<list of top 3 most important improvements>]
}}
"""

        # Call AI for validation analysis
        debug("Sending test case to AI for completeness validation")
        ai_response = generate_llm_response(
            prompt=validation_prompt,
            model_name="gemini-2.0-flash",
            api_key=api_key,
            category="test_case_completeness_validation",
            context={
                'test_case_id': test_case_summary['id'],
                'original_step_count': len(test_case_summary['original_steps']),
                'converted_step_count': len(test_case_summary['converted_steps']),
                'operation': 'completeness_validation'
            }
        )

        if not ai_response:
            debug("AI validation returned empty response")
            return _get_fallback_validation_result("AI service unavailable")

        # Parse AI response
        try:
            # Clean the response to extract JSON
            import re
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                clean_response = json_match.group(0)
                validation_result = json.loads(clean_response)
                debug(f"AI validation completed successfully with score: {validation_result.get('overall_completeness_score', 'N/A')}")
                return validation_result
            else:
                debug("Could not extract JSON from AI response")
                return _get_fallback_validation_result("Invalid AI response format")

        except json.JSONDecodeError as e:
            debug(f"Failed to parse AI validation response: {e}")
            return _get_fallback_validation_result("Failed to parse AI response")

    except Exception as e:
        logger.error(f"Error in AI test case completeness validation: {e}")
        debug(f"AI validation error: {e}")
        return _get_fallback_validation_result(f"Validation error: {str(e)}")


def _get_fallback_validation_result(error_reason):
    """
    Provide a fallback validation result when AI validation fails.

    Args:
        error_reason (str): Reason for fallback

    Returns:
        dict: Fallback validation result
    """
    return {
        "overall_completeness_score": 7,  # Neutral score
        "is_complete": True,  # Assume complete to not block workflow
        "validation_results": {
            "navigation": {
                "has_navigation": True,
                "issues": [],
                "recommendations": ["Verify navigation steps are present"]
            },
            "step_flow": {
                "is_logical": True,
                "missing_steps": [],
                "sequence_issues": [],
                "recommendations": ["Review step sequence manually"]
            },
            "end_to_end": {
                "has_completion": True,
                "has_verification": True,
                "missing_assertions": [],
                "recommendations": ["Verify completion steps are adequate"]
            },
            "critical_gaps": []
        },
        "summary": f"AI validation unavailable ({error_reason}). Manual review recommended.",
        "priority_improvements": [
            "Manually verify navigation steps",
            "Check step sequence logic",
            "Ensure adequate verification steps"
        ]
    }


def classify_validation_issues_with_ai(validation_results: Dict, test_case: Dict, api_key: Optional[str] = None) -> Dict:
    """
    Use AI to classify validation issues as solvable (auto-fixable) or unsolvable (requiring regeneration).

    Args:
        validation_results (Dict): Results from AI completeness validation
        test_case (Dict): Original test case data
        api_key (Optional[str]): Google AI API key

    Returns:
        Dict: Classification results with auto-fix suggestions and regeneration recommendations
    """
    try:
        debug("Starting AI-powered issue classification")

        # Extract all issues from validation results
        validation_data = validation_results.get('validation_results', {})
        all_issues = []

        # Collect navigation issues
        nav_issues = validation_data.get('navigation', {}).get('issues', [])
        all_issues.extend([{'category': 'navigation', 'issue': issue} for issue in nav_issues])

        # Collect step flow issues
        flow_data = validation_data.get('step_flow', {})
        missing_steps = flow_data.get('missing_steps', [])
        sequence_issues = flow_data.get('sequence_issues', [])
        all_issues.extend([{'category': 'missing_steps', 'issue': step} for step in missing_steps])
        all_issues.extend([{'category': 'sequence', 'issue': issue} for issue in sequence_issues])

        # Collect end-to-end issues
        e2e_data = validation_data.get('end_to_end', {})
        missing_assertions = e2e_data.get('missing_assertions', [])
        all_issues.extend([{'category': 'assertions', 'issue': assertion} for assertion in missing_assertions])

        # Collect critical gaps
        critical_gaps = validation_data.get('critical_gaps', [])
        all_issues.extend([{'category': 'critical', 'issue': gap} for gap in critical_gaps])

        if not all_issues:
            debug("No issues found for classification")
            return {
                'solvable_issues': [],
                'unsolvable_issues': [],
                'auto_fix_suggestions': [],
                'regeneration_required': False,
                'classification_summary': 'No issues detected'
            }

        # Create AI prompt for issue classification
        classification_prompt = f"""
Analyze these test case validation issues and classify them as either SOLVABLE (can be auto-fixed) or UNSOLVABLE (require regeneration).

TEST CASE CONTEXT:
Test Case ID: {test_case.get('Test Case ID', 'Unknown')}
Objective: {test_case.get('Test Case Objective', '')}
Original Steps Count: {len(test_case.get('Steps', []))}

VALIDATION ISSUES TO CLASSIFY:
{json.dumps(all_issues, indent=2)}

CLASSIFICATION CRITERIA:

SOLVABLE ISSUES (Auto-fixable):
- Incorrect UI element names/selectors (can be corrected through element detection)
- Missing test data that can be populated from context
- Minor step sequence adjustments that don't change core logic
- Formatting inconsistencies in automation steps
- Simple assertion additions for existing actions
- Basic navigation corrections (URL formatting, etc.)

UNSOLVABLE ISSUES (Require regeneration):
- Fundamental logical gaps in test flow
- Missing critical steps requiring domain knowledge
- Incompatible test objectives needing restructuring
- Complex business logic errors requiring human input
- Major workflow changes affecting test purpose
- Missing entire test phases (setup, execution, verification)

For each issue, provide:
1. Classification (solvable/unsolvable)
2. Confidence level (1-10)
3. Auto-fix suggestion (if solvable)
4. Regeneration reason (if unsolvable)

RESPONSE FORMAT:
Return ONLY valid JSON in this exact format:
{{
    "solvable_issues": [
        {{
            "category": "<category>",
            "issue": "<issue description>",
            "confidence": <1-10>,
            "auto_fix_suggestion": "<specific fix suggestion>",
            "fix_type": "<selector_fix|data_population|sequence_adjustment|formatting>"
        }}
    ],
    "unsolvable_issues": [
        {{
            "category": "<category>",
            "issue": "<issue description>",
            "confidence": <1-10>,
            "regeneration_reason": "<why regeneration is needed>",
            "complexity": "<low|medium|high>"
        }}
    ],
    "auto_fix_suggestions": [
        "<actionable auto-fix recommendations>"
    ],
    "regeneration_required": <boolean>,
    "classification_summary": "<brief summary of classification results>",
    "recommended_action": "<auto_fix|manual_edit|regenerate|hybrid>"
}}
"""

        # Call AI for issue classification
        debug("Sending issues to AI for classification")
        ai_response = generate_llm_response(
            prompt=classification_prompt,
            model_name="gemini-2.0-flash",
            api_key=api_key,
            category="issue_classification",
            context={
                'test_case_id': test_case.get('Test Case ID', 'Unknown'),
                'total_issues': len(all_issues),
                'operation': 'issue_classification'
            }
        )

        if not ai_response:
            debug("AI classification returned empty response")
            return _get_fallback_classification_result("AI service unavailable")

        # Parse AI response
        try:
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                clean_response = json_match.group(0)
                classification_result = json.loads(clean_response)
                debug(f"AI classification completed: {classification_result.get('recommended_action', 'unknown')} action recommended")
                return classification_result
            else:
                debug("Could not extract JSON from AI classification response")
                return _get_fallback_classification_result("Invalid AI response format")

        except json.JSONDecodeError as e:
            debug(f"Failed to parse AI classification response: {e}")
            return _get_fallback_classification_result("Failed to parse AI response")

    except Exception as e:
        logger.error(f"Error in AI issue classification: {e}")
        debug(f"AI classification error: {e}")
        return _get_fallback_classification_result(f"Classification error: {str(e)}")


def _get_fallback_classification_result(error_reason: str) -> Dict:
    """
    Provide a fallback classification result when AI classification fails.

    Args:
        error_reason (str): Reason for fallback

    Returns:
        Dict: Fallback classification result
    """
    return {
        'solvable_issues': [],
        'unsolvable_issues': [],
        'auto_fix_suggestions': [],
        'regeneration_required': False,
        'classification_summary': f'Classification unavailable ({error_reason}). Manual review recommended.',
        'recommended_action': 'manual_edit'
    }


def regenerate_test_case_with_ai(original_test_case: Dict, validation_feedback: Dict,
                                classification_results: Dict, user_comments: str = "",
                                api_key: Optional[str] = None) -> Tuple[Optional[str], Optional[List]]:
    """
    Intelligently regenerate test case using AI with enhanced prompts that include validation feedback.

    Args:
        original_test_case (Dict): Original test case data
        validation_feedback (Dict): Results from completeness validation
        classification_results (Dict): Issue classification results
        user_comments (str): Additional user requirements/comments
        api_key (Optional[str]): Google AI API key

    Returns:
        Tuple[Optional[str], Optional[List]]: (markdown_table, json_table) or (None, None) if failed
    """
    try:
        debug("Starting intelligent test case regeneration")

        # Prepare regeneration context
        test_case_id = original_test_case.get('Test Case ID', 'Unknown')
        objective = original_test_case.get('Test Case Objective', '')
        original_steps = original_test_case.get('Steps', [])

        # Extract validation issues for regeneration guidance
        unsolvable_issues = classification_results.get('unsolvable_issues', [])
        critical_gaps = validation_feedback.get('validation_results', {}).get('critical_gaps', [])
        priority_improvements = validation_feedback.get('priority_improvements', [])

        # Create enhanced regeneration prompt
        regeneration_prompt = f"""
INTELLIGENT TEST CASE REGENERATION

You are tasked with regenerating a test case that has validation issues requiring complete restructuring.

ORIGINAL TEST CASE:
Test Case ID: {test_case_id}
Objective: {objective}

Original Steps:
{json.dumps([{{'step_no': step.get('Step No', ''), 'action': step.get('Test Steps', ''), 'expected': step.get('Expected Result', '')}} for step in original_steps], indent=2)}

VALIDATION FEEDBACK:
Overall Completeness Score: {validation_feedback.get('overall_completeness_score', 'N/A')}/10
Summary: {validation_feedback.get('summary', 'No summary available')}

CRITICAL ISSUES REQUIRING REGENERATION:
{json.dumps(unsolvable_issues, indent=2)}

CRITICAL GAPS IDENTIFIED:
{json.dumps(critical_gaps, indent=2)}

PRIORITY IMPROVEMENTS NEEDED:
{json.dumps(priority_improvements, indent=2)}

USER REQUIREMENTS/COMMENTS:
{user_comments if user_comments else "No additional requirements provided"}

REGENERATION INSTRUCTIONS:

1. MAINTAIN CORE OBJECTIVE: Keep the original test case objective but restructure the approach
2. ADDRESS ALL CRITICAL ISSUES: Specifically resolve each unsolvable issue identified
3. FILL CRITICAL GAPS: Add missing elements that were flagged as critical
4. IMPROVE COMPLETENESS: Implement all priority improvements
5. ENSURE END-TO-END FLOW: Create a complete test flow from start to finish

ENHANCED REQUIREMENTS:
- Start with proper navigation/setup steps
- Include all necessary intermediate steps with logical flow
- Add comprehensive verification/assertion steps
- Ensure test data is realistic and complete
- Include error handling scenarios where appropriate
- Make steps automation-ready with clear actions and expected results

OUTPUT FORMAT:
Generate a complete automation-ready step table in both markdown and JSON formats.

MARKDOWN TABLE FORMAT:
| Step No | Action | Locator Strategy | Locator Value | Test Data | Expected Result | Assertion Type |
|---------|--------|------------------|---------------|-----------|-----------------|----------------|
| 1 | Navigate to login page | url | https://example.com/login | | Login page loads | page_title |
| 2 | Enter username | id | username | <EMAIL> | Username entered | element_value |
| ... | ... | ... | ... | ... | ... | ... |

JSON FORMAT:
[
  {{
    "step_no": "1",
    "action": "Navigate to login page",
    "locator_strategy": "url",
    "locator_value": "https://example.com/login",
    "test_data": "",
    "expected_result": "Login page loads",
    "assertion_type": "page_title"
  }},
  ...
]

RESPONSE FORMAT:
Return your response in this exact format:

MARKDOWN_TABLE:
[Your markdown table here]

JSON_TABLE:
[Your JSON array here]
"""

        # Call AI for intelligent regeneration
        debug(f"Sending test case for intelligent regeneration: {test_case_id}")
        ai_response = generate_llm_response(
            prompt=regeneration_prompt,
            model_name="gemini-2.0-flash",
            api_key=api_key,
            category="intelligent_test_case_regeneration",
            context={
                'test_case_id': test_case_id,
                'original_step_count': len(original_steps),
                'unsolvable_issues_count': len(unsolvable_issues),
                'critical_gaps_count': len(critical_gaps),
                'operation': 'intelligent_regeneration',
                'has_user_comments': bool(user_comments)
            }
        )

        if not ai_response:
            debug("AI regeneration returned empty response")
            return None, None

        # Parse the regenerated response
        try:
            # Extract markdown table
            markdown_match = re.search(r'MARKDOWN_TABLE:\s*(.*?)\s*JSON_TABLE:', ai_response, re.DOTALL)
            if not markdown_match:
                debug("Could not extract markdown table from regeneration response")
                return None, None

            markdown_table = markdown_match.group(1).strip()

            # Extract JSON table
            json_match = re.search(r'JSON_TABLE:\s*(\[.*?\])', ai_response, re.DOTALL)
            if not json_match:
                debug("Could not extract JSON table from regeneration response")
                return None, None

            json_str = json_match.group(1).strip()
            json_table = json.loads(json_str)

            debug(f"AI regeneration completed successfully: {len(json_table)} steps generated")
            return markdown_table, json_table

        except json.JSONDecodeError as e:
            debug(f"Failed to parse JSON from regeneration response: {e}")
            return None, None
        except Exception as e:
            debug(f"Error parsing regeneration response: {e}")
            return None, None

    except Exception as e:
        logger.error(f"Error in intelligent test case regeneration: {e}")
        debug(f"AI regeneration error: {e}")
        return None, None


def apply_auto_fixes_with_ai(step_table_json: List[Dict], solvable_issues: List[Dict],
                            api_key: Optional[str] = None) -> Tuple[Optional[str], Optional[List]]:
    """
    Apply automatic fixes to solvable issues in the test case.

    Args:
        step_table_json (List[Dict]): Current step table in JSON format
        solvable_issues (List[Dict]): Issues that can be automatically fixed
        api_key (Optional[str]): Google AI API key

    Returns:
        Tuple[Optional[str], Optional[List]]: (fixed_markdown, fixed_json) or (None, None) if failed
    """
    try:
        debug("Starting automatic issue fixes")

        if not solvable_issues:
            debug("No solvable issues to fix")
            return None, None

        # Create auto-fix prompt
        auto_fix_prompt = f"""
AUTOMATIC TEST CASE ISSUE FIXES

Apply automatic fixes to the following solvable issues in this test case step table.

CURRENT STEP TABLE:
{json.dumps(step_table_json, indent=2)}

SOLVABLE ISSUES TO FIX:
{json.dumps(solvable_issues, indent=2)}

AUTO-FIX INSTRUCTIONS:

For each solvable issue, apply the suggested fix:

1. SELECTOR FIXES: Correct UI element names/selectors based on common patterns
2. DATA POPULATION: Add realistic test data where missing
3. SEQUENCE ADJUSTMENTS: Reorder steps for logical flow without changing core logic
4. FORMATTING: Standardize step formatting and structure
5. ASSERTION ADDITIONS: Add simple assertions for existing actions

RULES:
- Only make the specific fixes suggested for each issue
- Do not change the core test logic or objective
- Maintain all existing steps unless specifically fixing sequence
- Use realistic, appropriate test data
- Ensure all fixes are automation-compatible

OUTPUT FORMAT:
Return the fixed step table in both markdown and JSON formats.

MARKDOWN_TABLE:
[Fixed markdown table here]

JSON_TABLE:
[Fixed JSON array here]
"""

        # Call AI for auto-fixes
        debug(f"Applying auto-fixes for {len(solvable_issues)} solvable issues")
        ai_response = generate_llm_response(
            prompt=auto_fix_prompt,
            model_name="gemini-2.0-flash",
            api_key=api_key,
            category="auto_fix_test_case",
            context={
                'current_step_count': len(step_table_json),
                'solvable_issues_count': len(solvable_issues),
                'operation': 'auto_fix'
            }
        )

        if not ai_response:
            debug("AI auto-fix returned empty response")
            return None, None

        # Parse the fixed response
        try:
            # Extract markdown table
            markdown_match = re.search(r'MARKDOWN_TABLE:\s*(.*?)\s*JSON_TABLE:', ai_response, re.DOTALL)
            if not markdown_match:
                debug("Could not extract markdown table from auto-fix response")
                return None, None

            fixed_markdown = markdown_match.group(1).strip()

            # Extract JSON table
            json_match = re.search(r'JSON_TABLE:\s*(\[.*?\])', ai_response, re.DOTALL)
            if not json_match:
                debug("Could not extract JSON table from auto-fix response")
                return None, None

            json_str = json_match.group(1).strip()
            fixed_json = json.loads(json_str)

            debug(f"Auto-fixes applied successfully: {len(fixed_json)} steps in fixed table")
            return fixed_markdown, fixed_json

        except json.JSONDecodeError as e:
            debug(f"Failed to parse JSON from auto-fix response: {e}")
            return None, None
        except Exception as e:
            debug(f"Error parsing auto-fix response: {e}")
            return None, None

    except Exception as e:
        logger.error(f"Error in automatic issue fixes: {e}")
        debug(f"Auto-fix error: {e}")
        return None, None
